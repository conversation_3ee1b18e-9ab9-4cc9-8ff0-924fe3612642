#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AirSim COCO数据集生成器使用示例

演示如何使用新的配置文件系统和处理器
"""

import os
import yaml
import shutil
from pathlib import Path
from airsim_coco_generator import AirSimCOCOProcessor


def create_example_config():
    """创建示例配置文件"""
    config = {
        'project': {
            'name': 'example_marine_dataset',
            'description': 'AirSim海洋仿真语义分割数据集示例',
            'version': '1.0',
            'author': 'Example User'
        },
        'input': {
            'csv_file': 'airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv',
            'mode': 'single',
            'single_image': 'fishmask.jpg',
            'batch_directory': 'images/',
            'supported_formats': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        },
        'detection': {
            'target_keywords': ['yu', 'diao', 'shark', 'shayu', 'feiji', 'longye', 'douyu'],
            'min_area': 100,
            'show_unmatched_objects': True,
            'name_mapping': {
                'tongshiguiyu': '铜师鬼鱼',
                'shitoujinyu': '石头金鱼',
                'hongdidiao': '红底雕',
                'niluokoufeiji': '尼罗口飞鸡',
                'chixuehonglongye': '赤血红龙叶',
                'yinnihuyu': '印尼虎鱼',
                'shuangdaigongziaohei': '双带公雕黑',
                'shiwangdouyu': '狮王斗鱼',
                'shayu': '鲨鱼'
            }
        },
        'output': {
            'base_directory': 'example_output_v2',
            'subdirectories': {
                'coco': 'coco',
                'visualizations': 'visualizations',
                'logs': 'logs',
                'reports': 'reports'
            },
            'coco': {
                'generate': True,
                'filename': 'dataset.json',
                'include_segmentation': True,
                'include_bbox': True
            },
            'visualization': {
                'generate': True,
                'show_bbox': True,
                'show_segmentation': True,
                'show_labels': True,
                'show_statistics': True,
                'dpi': 300,
                'font_size': 12
            },
            'reports': {
                'generate': True,
                'include_statistics': True,
                'include_color_mapping': True,
                'format': 'json'
            }
        },
        'dataset_split': {
            'enable': False,
            'train_ratio': 0.7,
            'val_ratio': 0.2,
            'test_ratio': 0.1,
            'random_seed': 42
        },
        'logging': {
            'level': 'INFO',
            'console_output': True,
            'file_output': True,
            'log_filename': 'processing.log'
        },
        'performance': {
            'max_workers': 4,
            'memory_limit_mb': 2048
        },
        'chinese_support': {
            'enable': True,
            'font_family': ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS'],
            'encoding': 'utf-8'
        },
        'debug': {
            'save_intermediate_files': False,
            'verbose_output': False,
            'color_analysis': False
        }
    }
    
    return config


def run_single_image_example():
    """运行单张图像处理示例"""
    print("🎯 运行单张图像处理示例")
    print("="*50)
    
    # 检查必需文件
    csv_file = 'airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv'
    image_file = 'fishmask.jpg'
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    if not os.path.exists(image_file):
        print(f"❌ 图像文件不存在: {image_file}")
        return False
    
    # 创建配置文件
    config = create_example_config()
    config['input']['mode'] = 'single'
    config['input']['single_image'] = image_file
    config['output']['base_directory'] = 'example_output_single'
    
    config_path = 'example_config_single.yaml'
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    try:
        # 运行处理器
        processor = AirSimCOCOProcessor(config_path)
        processor.run()
        
        print(f"\n✅ 单张图像处理完成！")
        print(f"📂 输出目录: {config['output']['base_directory']}")
        
        # 清理配置文件
        os.remove(config_path)
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        if os.path.exists(config_path):
            os.remove(config_path)
        return False


def run_batch_processing_example():
    """运行批量处理示例"""
    print("\n🎯 运行批量处理示例")
    print("="*50)
    
    # 检查CSV文件
    csv_file = 'airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    # 创建示例图像目录
    images_dir = Path('example_images')
    images_dir.mkdir(exist_ok=True)
    
    # 复制现有图像到示例目录
    source_image = 'fishmask.jpg'
    if os.path.exists(source_image):
        for i in range(3):
            target_image = images_dir / f'sample_{i+1}.jpg'
            shutil.copy2(source_image, target_image)
            print(f"📋 创建示例图像: {target_image}")
    else:
        print(f"❌ 源图像不存在: {source_image}")
        return False
    
    # 创建配置文件
    config = create_example_config()
    config['input']['mode'] = 'batch'
    config['input']['batch_directory'] = str(images_dir)
    config['output']['base_directory'] = 'example_output_batch'
    config['dataset_split']['enable'] = True  # 启用数据集划分
    
    config_path = 'example_config_batch.yaml'
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    try:
        # 运行处理器
        processor = AirSimCOCOProcessor(config_path)
        processor.run()
        
        print(f"\n✅ 批量处理完成！")
        print(f"📂 输出目录: {config['output']['base_directory']}")
        
        # 清理
        os.remove(config_path)
        shutil.rmtree(images_dir)
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        if os.path.exists(config_path):
            os.remove(config_path)
        if images_dir.exists():
            shutil.rmtree(images_dir)
        return False


def run_custom_keywords_example():
    """运行自定义关键词示例"""
    print("\n🎯 运行自定义关键词示例")
    print("="*50)
    
    # 检查必需文件
    csv_file = 'airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv'
    image_file = 'fishmask.jpg'
    
    if not os.path.exists(csv_file) or not os.path.exists(image_file):
        print(f"❌ 必需文件不存在")
        return False
    
    # 创建配置文件 - 只检测环境对象
    config = create_example_config()
    config['input']['mode'] = 'single'
    config['input']['single_image'] = image_file
    config['output']['base_directory'] = 'example_output_environment'
    
    # 修改目标关键词为环境对象
    config['detection']['target_keywords'] = ['boulder', 'ground', 'water', 'floor']
    config['detection']['name_mapping'] = {
        'boulder': '巨石',
        'ground': '地面',
        'water': '水体',
        'floor': '地板'
    }
    
    config_path = 'example_config_environment.yaml'
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    try:
        # 运行处理器
        processor = AirSimCOCOProcessor(config_path)
        processor.run()
        
        print(f"\n✅ 环境对象检测完成！")
        print(f"📂 输出目录: {config['output']['base_directory']}")
        
        # 清理配置文件
        os.remove(config_path)
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        if os.path.exists(config_path):
            os.remove(config_path)
        return False


def main():
    """主函数"""
    print("🚀 AirSim COCO数据集生成器使用示例")
    print("="*60)
    
    examples = [
        ("单张图像处理", run_single_image_example),
        ("批量处理", run_batch_processing_example),
        ("自定义关键词检测", run_custom_keywords_example)
    ]
    
    results = []
    
    for name, func in examples:
        try:
            result = func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 执行失败: {e}")
            results.append((name, False))
    
    # 打印总结
    print("\n" + "="*60)
    print("📊 示例执行总结:")
    print("="*60)
    
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {name}: {status}")
    
    successful_count = sum(1 for _, success in results if success)
    print(f"\n🎯 成功执行: {successful_count}/{len(results)} 个示例")
    
    if successful_count > 0:
        print(f"\n💡 使用说明:")
        print(f"   1. 复制 config_template.yaml 并修改配置")
        print(f"   2. 运行: python airsim_coco_generator.py --config your_config.yaml")
        print(f"   3. 查看输出目录中的结果文件")
    
    print(f"\n🎉 示例演示完成！")


if __name__ == "__main__":
    main()
