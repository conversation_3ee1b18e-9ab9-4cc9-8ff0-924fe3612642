#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COCO数据集验证启动脚本

从项目根目录便捷启动验证工具
"""

import os
import sys
import subprocess
import argparse


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='COCO数据集验证启动脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 完整验证
  python validate.py --coco output/coco/dataset.json --images . --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv

  # 快速验证（跳过掩码验证）
  python validate.py --coco output/coco/dataset.json --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv --skip-mask

  # 仅COCO格式验证
  python validate.py --coco output/coco/dataset.json --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv --coco-only

  # 增强掩码验证（实例级+类别级）
  python validate.py --coco output/coco/dataset.json --images . --enhanced-mask

  # 仅类别级掩码验证
  python validate.py --coco output/coco/dataset.json --images . --enhanced-mask --no-instance
        """
    )
    
    parser.add_argument('--coco', required=True, help='COCO JSON文件路径')
    parser.add_argument('--images', help='图像目录路径')
    parser.add_argument('--csv', help='原始CSV文件路径')
    parser.add_argument('--config', help='验证配置文件路径')
    parser.add_argument('--output', help='输出目录')
    parser.add_argument('--skip-mask', action='store_true', help='跳过二值掩码验证')
    parser.add_argument('--coco-only', action='store_true', help='仅运行COCO格式验证')
    parser.add_argument('--mask-only', action='store_true', help='仅运行二值掩码验证')
    parser.add_argument('--enhanced-mask', action='store_true', help='使用增强掩码验证（支持类别级掩码）')
    parser.add_argument('--strict', action='store_true', help='启用严格模式')
    parser.add_argument('--no-viz', action='store_true', help='禁用可视化生成')
    parser.add_argument('--no-category', action='store_true', help='禁用类别级掩码生成')
    parser.add_argument('--no-instance', action='store_true', help='禁用实例级掩码生成')
    
    args = parser.parse_args()
    
    # 检查validation_tools目录
    validation_tools_dir = os.path.join(os.path.dirname(__file__), 'validation_tools')
    if not os.path.exists(validation_tools_dir):
        print("❌ 错误: validation_tools 目录不存在")
        print("请确保验证工具已正确安装在 validation_tools/ 目录下")
        sys.exit(1)
    
    # 检查输入文件
    if not os.path.exists(args.coco):
        print(f"❌ COCO文件不存在: {args.coco}")
        sys.exit(1)
    
    if args.images and not os.path.exists(args.images):
        print(f"❌ 图像目录不存在: {args.images}")
        sys.exit(1)
    
    if args.csv and not os.path.exists(args.csv):
        print(f"❌ CSV文件不存在: {args.csv}")
        sys.exit(1)
    
    # 构建命令
    if args.coco_only:
        # 仅COCO格式验证
        script_path = os.path.join(validation_tools_dir, 'validate_coco_dataset.py')
        cmd = ['python', script_path, '--coco', args.coco]
        
        if args.images:
            cmd.extend(['--images', args.images])
        if args.csv:
            cmd.extend(['--csv', args.csv])
        if args.config:
            cmd.extend(['--config', args.config])
        if args.output:
            cmd.extend(['--output', args.output])
        if args.strict:
            cmd.append('--strict')
        if args.no_viz:
            cmd.append('--no-viz')
            
    elif args.mask_only or args.enhanced_mask:
        # 二值掩码验证或增强掩码验证
        if not args.images:
            print("❌ 掩码验证需要提供图像目录 (--images)")
            sys.exit(1)

        # 选择使用增强版本还是标准版本
        if args.enhanced_mask:
            script_path = os.path.join(validation_tools_dir, 'enhanced_mask_validator.py')
            output_name = 'enhanced_mask_validation'
        else:
            script_path = os.path.join(validation_tools_dir, 'binary_mask_validator.py')
            output_name = 'mask_validation'

        cmd = ['python', script_path, '--coco', args.coco, '--images', args.images]

        if args.output:
            cmd.extend(['--output', args.output])
        else:
            cmd.extend(['--output', output_name])

        # 添加增强掩码特有的选项
        if args.enhanced_mask:
            if args.config:
                cmd.extend(['--config', args.config])
            if args.no_category:
                cmd.append('--no-category')
            if args.no_instance:
                cmd.append('--no-instance')
            if args.no_viz:
                cmd.append('--no-comparison')

    else:
        # 完整验证（默认）
        script_path = os.path.join(validation_tools_dir, 'run_validation.py')
        cmd = ['python', script_path, '--coco', args.coco]
        
        if args.images:
            cmd.extend(['--images', args.images])
        if args.csv:
            cmd.extend(['--csv', args.csv])
        if args.config:
            cmd.extend(['--config', args.config])
        else:
            # 使用默认配置文件
            default_config = os.path.join(validation_tools_dir, 'validation_config.yaml')
            if os.path.exists(default_config):
                cmd.extend(['--config', default_config])
        if args.output:
            cmd.extend(['--output', args.output])
        if args.skip_mask:
            cmd.append('--skip-mask')
    
    # 切换到validation_tools目录执行
    original_cwd = os.getcwd()
    
    try:
        print(f"🚀 启动验证工具...")
        print(f"📂 工作目录: {validation_tools_dir}")
        print(f"🔧 执行命令: {' '.join(cmd[1:])}")  # 不显示python部分
        print()
        
        # 切换目录并执行
        os.chdir(validation_tools_dir)
        
        # 调整命令中的相对路径
        adjusted_cmd = []
        for i, arg in enumerate(cmd):
            if i == 0:  # python
                adjusted_cmd.append(arg)
            elif i == 1:  # script path
                adjusted_cmd.append(os.path.basename(arg))
            elif arg.startswith('--'):
                adjusted_cmd.append(arg)
            elif i > 1 and cmd[i-1] in ['--coco', '--images', '--csv', '--config', '--output']:
                # 调整文件路径为相对于validation_tools的路径
                if arg.startswith('/'):
                    # 绝对路径，不变
                    adjusted_cmd.append(arg)
                else:
                    # 相对路径，添加../前缀
                    adjusted_cmd.append(os.path.join('..', arg))
            else:
                adjusted_cmd.append(arg)
        
        # 执行命令
        result = subprocess.run(adjusted_cmd, check=False)
        sys.exit(result.returncode)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断验证过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 验证启动失败: {str(e)}")
        sys.exit(1)
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)


if __name__ == "__main__":
    main()
