#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的二值掩码验证工具

支持生成实例级和类别级聚合掩码，用于不同的分析需求
"""

import json
import cv2
import numpy as np
import os
import yaml
from typing import Dict, List, Any, Tuple
import matplotlib.pyplot as plt
from PIL import Image
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB']
plt.rcParams['axes.unicode_minus'] = False


class EnhancedMaskValidator:
    """增强的二值掩码验证器（支持实例级和类别级掩码）"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        self.validation_results = {
            'total_masks': 0,
            'successful_reconstructions': 0,
            'failed_reconstructions': 0,
            'pixel_accuracy_scores': [],
            'iou_scores': [],
            'detailed_results': [],
            'category_masks': {},
            'instance_masks': {}
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'mask_generation': {
                'generate_instance_masks': True,
                'generate_category_masks': True,
                'generate_comparisons': True,
                'output_structure': {
                    'instance_dir': 'instance_masks',
                    'category_dir': 'category_masks',
                    'comparison_dir': 'comparisons'
                },
                'visualization': {
                    'show_statistics': True,
                    'max_categories_per_row': 4,
                    'figure_dpi': 150
                }
            }
        }
    
    def validate_masks(self, coco_path: str, images_dir: str, output_dir: str = "enhanced_mask_validation") -> Dict[str, Any]:
        """
        验证COCO分割数据并生成实例级和类别级掩码
        
        Args:
            coco_path: COCO JSON文件路径
            images_dir: 原始分割图像目录
            output_dir: 输出目录
            
        Returns:
            验证结果字典
        """
        print("🔍 开始增强掩码验证和生成...")
        
        # 创建输出目录结构
        self._create_output_directories(output_dir)
        
        # 加载COCO数据
        with open(coco_path, 'r', encoding='utf-8') as f:
            coco_data = json.load(f)
        
        images = coco_data.get('images', [])
        annotations = coco_data.get('annotations', [])
        categories = coco_data.get('categories', [])
        
        # 构建类别映射
        category_map = {cat['id']: cat['name'] for cat in categories}
        
        # 按图像分组标注
        image_annotations = defaultdict(list)
        for ann in annotations:
            image_annotations[ann['image_id']].append(ann)
        
        # 处理每张图像
        for image in images:
            image_id = image['id']
            if image_id in image_annotations:
                self._process_image_masks(
                    image, image_annotations[image_id], category_map, 
                    images_dir, output_dir
                )
        
        # 计算总体统计
        self._calculate_overall_statistics()
        
        return self.validation_results
    
    def _create_output_directories(self, output_dir: str) -> None:
        """创建输出目录结构"""
        config = self.config.get('mask_generation', {})

        os.makedirs(output_dir, exist_ok=True)

        if config.get('generate_instance_masks', True):
            instance_dir = os.path.join(output_dir, config.get('output_structure', {}).get('instance_dir', 'instance_masks'))
            os.makedirs(instance_dir, exist_ok=True)

        if config.get('generate_category_masks', True):
            category_dir = os.path.join(output_dir, config.get('output_structure', {}).get('category_dir', 'category_masks'))
            os.makedirs(category_dir, exist_ok=True)

        if config.get('generate_comparisons', True):
            comparison_dir = os.path.join(output_dir, config.get('output_structure', {}).get('comparison_dir', 'comparisons'))
            os.makedirs(comparison_dir, exist_ok=True)
    
    def _process_image_masks(self, image: Dict[str, Any], annotations: List[Dict[str, Any]],
                            category_map: Dict[int, str], images_dir: str, output_dir: str) -> None:
        """处理单张图像的掩码生成和验证"""
        image_path = os.path.join(images_dir, image['file_name'])
        
        if not os.path.exists(image_path):
            print(f"⚠️ 图像文件不存在: {image['file_name']}")
            return
        
        # 读取原始分割图像
        original_image = cv2.imread(image_path)
        if original_image is None:
            print(f"⚠️ 无法读取图像: {image['file_name']}")
            return
        
        original_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        height, width = original_rgb.shape[:2]
        
        print(f"📸 处理图像: {image['file_name']} ({width}x{height})")
        
        # 按类别分组标注
        category_annotations = defaultdict(list)
        for ann in annotations:
            if 'instance_color' in ann and 'category_id' in ann:
                category_annotations[ann['category_id']].append(ann)
        
        # 生成和验证实例级掩码
        instance_results = {}
        if self.config.get('mask_generation', {}).get('generate_instance_masks', True):
            instance_results = self._generate_and_validate_instance_masks(
                annotations, original_rgb, image['file_name'], output_dir
            )

        # 生成类别级掩码
        category_results = {}
        if self.config.get('mask_generation', {}).get('generate_category_masks', True):
            category_results = self._generate_category_masks(
                category_annotations, category_map, original_rgb,
                image['file_name'], output_dir
            )

        # 生成对比可视化
        if self.config.get('mask_generation', {}).get('generate_comparisons', True):
            self._generate_comparison_visualization(
                original_rgb, instance_results, category_results, category_map,
                image['file_name'], output_dir
            )
        
        # 保存结果
        self.validation_results['instance_masks'][image['file_name']] = instance_results
        self.validation_results['category_masks'][image['file_name']] = category_results
    
    def _generate_and_validate_instance_masks(self, annotations: List[Dict[str, Any]],
                                            original_rgb: np.ndarray, image_name: str,
                                            output_dir: str) -> Dict[str, Any]:
        """生成和验证实例级掩码"""
        instance_results = {}
        config = self.config.get('mask_generation', {})
        instance_dir = os.path.join(output_dir, config.get('output_structure', {}).get('instance_dir', 'instance_masks'))

        # 检测重复颜色
        color_to_annotations = defaultdict(list)
        for annotation in annotations:
            if 'instance_color' in annotation:
                color_key = tuple(annotation['instance_color'])
                color_to_annotations[color_key].append(annotation)

        # 报告重复颜色
        for color, anns in color_to_annotations.items():
            if len(anns) > 1:
                print(f"   ⚠️ 发现重复颜色 {color}: {len(anns)}个实例")
                for ann in anns:
                    print(f"      - 实例{ann['id']}: {ann.get('instance_name', 'unnamed')}")

        for annotation in annotations:
            if 'instance_color' not in annotation:
                continue
            
            # 提取实例颜色掩码
            target_color = tuple(annotation['instance_color'])
            instance_mask = np.all(original_rgb == target_color, axis=2)
            
            if not np.any(instance_mask):
                continue
            
            # 从COCO segmentation重建掩码进行对比
            height, width = original_rgb.shape[:2]
            reconstructed_mask = self._reconstruct_mask_from_segmentation(
                annotation.get('segmentation', []), width, height
            )
            
            # 计算验证指标
            metrics = self._calculate_mask_metrics(instance_mask, reconstructed_mask)
            
            # 保存实例掩码
            instance_name = annotation.get('instance_name', f'instance_{annotation["id"]}')
            # 使用ID而不是名称作为文件名，避免文件名过长
            mask_filename = f"{os.path.splitext(image_name)[0]}_instance_{annotation['id']}.png"
            mask_path = os.path.join(instance_dir, mask_filename)
            
            # 转换为0-255的二值图像
            mask_image = (instance_mask * 255).astype(np.uint8)
            cv2.imwrite(mask_path, mask_image)
            
            # 记录结果
            result = {
                'image_file': image_name,
                'annotation_id': annotation['id'],
                'instance_name': instance_name,
                'instance_color': annotation['instance_color'],
                'mask_path': mask_path,
                **metrics
            }
            
            instance_results[annotation['id']] = result
            self.validation_results['detailed_results'].append(result)
            
            # 更新统计
            self.validation_results['total_masks'] += 1
            self.validation_results['pixel_accuracy_scores'].append(metrics['pixel_accuracy'])
            self.validation_results['iou_scores'].append(metrics['iou'])
            
            if metrics['iou'] > 0.5:  # 成功阈值
                self.validation_results['successful_reconstructions'] += 1
                print(f"   ✅ {instance_name}: IoU={metrics['iou']:.3f}, 像素精度={metrics['pixel_accuracy']:.3f}")
            else:
                self.validation_results['failed_reconstructions'] += 1
                print(f"   ❌ {instance_name}: IoU={metrics['iou']:.3f}, 像素精度={metrics['pixel_accuracy']:.3f}")
        
        return instance_results

    def _generate_category_masks(self, category_annotations: Dict[int, List[Dict[str, Any]]],
                                category_map: Dict[int, str], original_rgb: np.ndarray,
                                image_name: str, output_dir: str) -> Dict[str, Any]:
        """生成类别级聚合掩码"""
        category_results = {}
        config = self.config.get('mask_generation', {})
        category_dir = os.path.join(output_dir, config.get('output_structure', {}).get('category_dir', 'category_masks'))

        for category_id, annotations in category_annotations.items():
            category_name = category_map.get(category_id, f'category_{category_id}')

            # 创建类别级掩码（合并所有实例）
            height, width = original_rgb.shape[:2]
            category_mask = np.zeros((height, width), dtype=bool)

            instance_count = 0
            total_area = 0

            for annotation in annotations:
                if 'instance_color' not in annotation:
                    continue

                # 提取实例颜色掩码
                target_color = tuple(annotation['instance_color'])
                instance_mask = np.all(original_rgb == target_color, axis=2)

                if np.any(instance_mask):
                    # 合并到类别掩码中
                    category_mask = np.logical_or(category_mask, instance_mask)
                    instance_count += 1
                    total_area += np.sum(instance_mask)

            if np.any(category_mask):
                # 保存类别掩码
                safe_name = self._make_safe_filename(category_name)
                mask_filename = f"{os.path.splitext(image_name)[0]}_{safe_name}_cat.png"
                mask_path = os.path.join(category_dir, mask_filename)

                # 转换为0-255的二值图像
                mask_image = (category_mask * 255).astype(np.uint8)
                cv2.imwrite(mask_path, mask_image)

                category_results[category_id] = {
                    'category_name': category_name,
                    'mask_path': mask_path,
                    'instance_count': instance_count,
                    'total_area': total_area,
                    'coverage_ratio': total_area / (height * width),
                    'category_area': int(np.sum(category_mask))
                }

                print(f"   📊 {category_name}: {instance_count}个实例, 覆盖率={total_area / (height * width):.1%}")

        return category_results

    def _generate_comparison_visualization(self, original_rgb: np.ndarray,
                                         instance_results: Dict[str, Any],
                                         category_results: Dict[str, Any],
                                         category_map: Dict[int, str],
                                         image_name: str, output_dir: str) -> None:
        """生成对比可视化"""
        if not category_results:
            return

        config = self.config.get('mask_generation', {})
        comparison_dir = os.path.join(output_dir, config.get('output_structure', {}).get('comparison_dir', 'comparisons'))

        # 计算子图数量
        num_categories = len(category_results)
        num_instances = len(instance_results)

        # 创建图像布局
        max_cols = config.get('visualization', {}).get('max_categories_per_row', 4)
        cols = min(max_cols, num_categories + 1)  # 原图 + 类别掩码
        rows = max(2, (num_categories + cols) // cols)

        fig, axes = plt.subplots(rows, cols, figsize=(cols * 4, rows * 3))
        if rows == 1:
            axes = axes.reshape(1, -1)
        elif cols == 1:
            axes = axes.reshape(-1, 1)

        # 显示原始图像
        axes[0, 0].imshow(original_rgb)
        axes[0, 0].set_title(f'原始图像\n{num_instances}个实例, {num_categories}个类别', fontsize=10)
        axes[0, 0].axis('off')

        # 显示类别掩码
        for i, (category_id, mask_info) in enumerate(category_results.items()):
            if i >= cols * rows - 1:
                break

            row = (i + 1) // cols
            col = (i + 1) % cols

            # 读取类别掩码
            mask_image = cv2.imread(mask_info['mask_path'], cv2.IMREAD_GRAYSCALE)

            axes[row, col].imshow(mask_image, cmap='gray')
            axes[row, col].set_title(
                f'{mask_info["category_name"]}\n'
                f'{mask_info["instance_count"]}个实例\n'
                f'覆盖率: {mask_info["coverage_ratio"]:.1%}',
                fontsize=10
            )
            axes[row, col].axis('off')

        # 隐藏多余的子图
        for i in range(len(category_results) + 1, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].axis('off')

        plt.tight_layout()

        # 保存对比图
        comparison_filename = f"{os.path.splitext(image_name)[0]}_enhanced_comparison.png"
        comparison_path = os.path.join(comparison_dir, comparison_filename)
        plt.savefig(comparison_path, dpi=config.get('visualization', {}).get('figure_dpi', 150), bbox_inches='tight')
        plt.close()

        print(f"   ✅ 生成增强对比图: {comparison_filename}")

    def _reconstruct_mask_from_segmentation(self, segmentation: List[List[float]],
                                           width: int, height: int) -> np.ndarray:
        """从COCO分割数据重建二值掩码"""
        mask = np.zeros((height, width), dtype=np.uint8)

        for polygon in segmentation:
            if len(polygon) >= 6:  # 至少3个点
                # 将坐标重塑为点对
                points = np.array(polygon).reshape(-1, 2).astype(np.int32)
                # 填充多边形
                cv2.fillPoly(mask, [points], 1)

        return mask.astype(bool)

    def _calculate_mask_metrics(self, original_mask: np.ndarray,
                               reconstructed_mask: np.ndarray) -> Dict[str, float]:
        """计算掩码精度指标"""
        # 确保掩码为布尔类型
        original_mask = original_mask.astype(bool)
        reconstructed_mask = reconstructed_mask.astype(bool)

        # 计算交集和并集
        intersection = np.logical_and(original_mask, reconstructed_mask)
        union = np.logical_or(original_mask, reconstructed_mask)

        # IoU (Intersection over Union)
        iou = np.sum(intersection) / np.sum(union) if np.sum(union) > 0 else 0.0

        # 像素精度 (Pixel Accuracy)
        correct_pixels = np.sum(original_mask == reconstructed_mask)
        total_pixels = original_mask.size
        pixel_accuracy = correct_pixels / total_pixels

        # Dice系数
        dice = 2 * np.sum(intersection) / (np.sum(original_mask) + np.sum(reconstructed_mask)) \
               if (np.sum(original_mask) + np.sum(reconstructed_mask)) > 0 else 0.0

        # 精确率和召回率
        true_positives = np.sum(intersection)
        false_positives = np.sum(reconstructed_mask) - true_positives
        false_negatives = np.sum(original_mask) - true_positives

        precision = true_positives / (true_positives + false_positives) \
                   if (true_positives + false_positives) > 0 else 0.0
        recall = true_positives / (true_positives + false_negatives) \
                if (true_positives + false_negatives) > 0 else 0.0

        return {
            'iou': float(iou),
            'pixel_accuracy': float(pixel_accuracy),
            'dice': float(dice),
            'precision': float(precision),
            'recall': float(recall),
            'original_area': int(np.sum(original_mask)),
            'reconstructed_area': int(np.sum(reconstructed_mask))
        }

    def _make_safe_filename(self, name: str) -> str:
        """创建安全的文件名"""
        # 移除或替换不安全的字符
        safe_name = name.replace('/', '_').replace('\\', '_').replace(':', '_')
        safe_name = safe_name.replace('<', '_').replace('>', '_').replace('|', '_')
        safe_name = safe_name.replace('?', '_').replace('*', '_').replace('"', '_')
        return safe_name[:50]  # 限制长度

    def _calculate_overall_statistics(self) -> None:
        """计算总体统计信息"""
        if self.validation_results['iou_scores']:
            self.validation_results['average_iou'] = np.mean(self.validation_results['iou_scores'])
            self.validation_results['min_iou'] = np.min(self.validation_results['iou_scores'])
            self.validation_results['max_iou'] = np.max(self.validation_results['iou_scores'])

        if self.validation_results['pixel_accuracy_scores']:
            self.validation_results['average_pixel_accuracy'] = np.mean(self.validation_results['pixel_accuracy_scores'])
            self.validation_results['min_pixel_accuracy'] = np.min(self.validation_results['pixel_accuracy_scores'])
            self.validation_results['max_pixel_accuracy'] = np.max(self.validation_results['pixel_accuracy_scores'])

        # 计算成功率
        total_masks = self.validation_results['total_masks']
        if total_masks > 0:
            self.validation_results['success_rate'] = self.validation_results['successful_reconstructions'] / total_masks

    def print_summary(self) -> None:
        """打印验证摘要"""
        print("\n" + "="*50)
        print("🎭 增强掩码验证结果")
        print("="*50)

        # 总体统计
        print(f"📊 总体统计:")
        print(f"   总掩码数: {self.validation_results['total_masks']}")
        print(f"   成功重建: {self.validation_results['successful_reconstructions']}")
        print(f"   失败重建: {self.validation_results['failed_reconstructions']}")
        print(f"   成功率: {self.validation_results.get('success_rate', 0):.1%}")

        # IoU统计
        if 'average_iou' in self.validation_results:
            print(f"\n📐 IoU统计:")
            print(f"   平均IoU: {self.validation_results['average_iou']:.3f}")
            print(f"   最小IoU: {self.validation_results['min_iou']:.3f}")
            print(f"   最大IoU: {self.validation_results['max_iou']:.3f}")

        # 像素精度统计
        if 'average_pixel_accuracy' in self.validation_results:
            print(f"\n🎯 像素精度统计:")
            print(f"   平均精度: {self.validation_results['average_pixel_accuracy']:.3f}")
            print(f"   最小精度: {self.validation_results['min_pixel_accuracy']:.3f}")
            print(f"   最大精度: {self.validation_results['max_pixel_accuracy']:.3f}")

        # 类别统计
        category_count = sum(len(masks) for masks in self.validation_results['category_masks'].values())
        instance_count = sum(len(masks) for masks in self.validation_results['instance_masks'].values())

        print(f"\n📋 掩码生成统计:")
        print(f"   实例级掩码: {instance_count}")
        print(f"   类别级掩码: {category_count}")

        # 精度较低的掩码
        problem_masks = [result for result in self.validation_results['detailed_results']
                        if result.get('iou', 0) < 0.8]

        if problem_masks:
            print(f"\n⚠️ 精度较低的掩码 (IoU < 0.8):")
            for mask in problem_masks[:5]:
                print(f"   - {mask['instance_name']}: IoU={mask['iou']:.3f}")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='增强的二值掩码验证工具（支持实例级和类别级掩码）')
    parser.add_argument('--coco', required=True, help='COCO JSON文件路径')
    parser.add_argument('--images', required=True, help='原始分割图像目录')
    parser.add_argument('--output', default='enhanced_mask_validation', help='输出目录')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--no-category', action='store_true', help='禁用类别级掩码生成')
    parser.add_argument('--no-instance', action='store_true', help='禁用实例级掩码生成')
    parser.add_argument('--no-comparison', action='store_true', help='禁用对比可视化')

    args = parser.parse_args()

    # 加载配置
    config = None
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

    # 应用命令行参数覆盖
    if config is None:
        config = {}
    if 'mask_generation' not in config:
        config['mask_generation'] = {}

    if args.no_category:
        config['mask_generation']['generate_category_masks'] = False
    if args.no_instance:
        config['mask_generation']['generate_instance_masks'] = False
    if args.no_comparison:
        config['mask_generation']['generate_comparisons'] = False

    # 创建验证器
    validator = EnhancedMaskValidator(config)

    # 运行验证
    results = validator.validate_masks(args.coco, args.images, args.output)

    # 打印结果
    validator.print_summary()

    # 保存结果（处理numpy类型）
    def convert_numpy_types(obj):
        """转换numpy类型为Python原生类型"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        return obj

    serializable_results = convert_numpy_types(results)

    with open(os.path.join(args.output, 'enhanced_mask_validation_results.json'), 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, ensure_ascii=False, indent=2)

    print(f"\n📄 详细结果已保存到: {args.output}/enhanced_mask_validation_results.json")


if __name__ == "__main__":
    main()
