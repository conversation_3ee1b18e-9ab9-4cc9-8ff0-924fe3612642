# COCO数据集验证工具

## 📋 概述

这是一套完整的COCO数据集验证工具，专门为AirSim实例分割到COCO格式转换设计，确保生成的数据集完全符合实例分割标准。

## 📁 文件结构

```
validation_tools/
├── README.md                    # 本文件
├── VALIDATION_GUIDE.md         # 详细使用指南
├── validation_config.yaml      # 验证配置文件
├── coco_validator.py           # 核心验证器类
├── validate_coco_dataset.py    # COCO格式验证主脚本
├── binary_mask_validator.py    # 二值掩码验证工具
├── enhanced_mask_validator.py  # 增强掩码验证工具（实例级+类别级）
├── run_validation.py           # 自动化验证框架（推荐使用）
└── validation_results/         # 验证结果输出目录
```

## 🚀 快速开始

### 1. 完整验证（推荐）

```bash
cd validation_tools
python run_validation.py \
    --coco ../output/coco/dataset.json \
    --images .. \
    --csv ../airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv
```

### 2. 仅COCO格式验证

```bash
cd validation_tools
python validate_coco_dataset.py \
    --coco ../output/coco/dataset.json \
    --images .. \
    --csv ../airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv
```

### 3. 仅二值掩码验证

```bash
cd validation_tools
python binary_mask_validator.py \
    --coco ../output/coco/dataset.json \
    --images .. \
    --output mask_validation
```

### 4. 增强掩码验证（实例级+类别级）

```bash
cd validation_tools
python enhanced_mask_validator.py \
    --coco ../output/coco/dataset.json \
    --images .. \
    --output enhanced_mask_validation
```

### 5. 从根目录使用增强掩码验证

```bash
# 完整的增强掩码验证
python validate.py --coco output/coco/dataset.json --images . --enhanced-mask

# 仅生成类别级掩码
python validate.py --coco output/coco/dataset.json --images . --enhanced-mask --no-instance

# 仅生成实例级掩码
python validate.py --coco output/coco/dataset.json --images . --enhanced-mask --no-category
```

## ✅ 验证功能

### 核心验证项目
- ✅ **COCO格式规范验证** - 严格验证JSON格式符合官方标准
- ✅ **数据关联性验证** - 检查ID映射和数据完整性
- ✅ **实例分割特异性验证** - 确保每个CSV实例独立转换
- ✅ **几何精度验证** - 验证bbox和segmentation的准确性
- ✅ **二值掩码验证** - 像素级精度对比验证
- ✅ **实例级掩码生成** - 每个实例独立的二值掩码（用于MMDetection训练）
- ✅ **类别级掩码生成** - 同类别所有实例合并的二值掩码（用于场景分析）
- ✅ **可视化生成** - 生成标注可视化和对比图像

### 特殊功能
- 🔍 **实例合并问题检测** - 能够发现错误的实例合并
- 📊 **性能监控** - 监控验证过程的内存和CPU使用
- 📋 **多格式报告** - 生成JSON和HTML格式的详细报告
- ⚙️ **配置化验证** - 支持自定义验证规则和阈值

## 📊 验证结果解读

### 成功标准
- ✅ **0个错误** - 完全符合COCO格式规范
- ✅ **缺失实例数 = 0** - 所有CSV实例都成功转换
- ✅ **几何精度问题 < 5%** - 大部分标注几何精度良好
- ✅ **掩码重建成功率 > 90%** - 分割数据准确可靠

### 常见问题
1. **实例合并问题**: 如果"缺失实例数"大于0，说明存在实例合并
2. **几何精度问题**: 大量bbox或分割精度不足
3. **格式错误**: JSON格式不符合COCO标准

## 🔧 配置说明

编辑 `validation_config.yaml` 来自定义验证参数：

```yaml
validation:
  strict_mode: true                    # 严格模式
  check_geometry: true                 # 启用几何精度验证
  generate_visualizations: true       # 生成可视化图像

geometry:
  iou_threshold: 0.8                  # IoU阈值
  min_area_threshold: 10              # 最小面积阈值
```

## 📁 输出文件

验证完成后，会在 `validation_results/` 目录下生成：

- `validation_summary.html` - 总结报告（推荐查看）
- `validation_report.json` - 详细验证结果
- `visualizations/` - 标注可视化图像
- `mask_validation/` - 掩码对比图像
- `performance_metrics.png` - 性能监控图表

## 💡 使用建议

1. **开发阶段**: 使用完整验证确保代码正确性
2. **生产阶段**: 可使用 `--skip-mask` 跳过掩码验证以提高速度
3. **问题排查**: 查看HTML报告获得详细信息
4. **性能优化**: 监控性能指标，优化处理流程

## 🆘 故障排除

### 常见错误

1. **模块导入错误**
   ```bash
   # 确保在validation_tools目录下运行
   cd validation_tools
   python run_validation.py --help
   ```

2. **路径问题**
   ```bash
   # 使用相对路径指向上级目录的文件
   --coco ../output/coco/dataset.json
   --images ..
   ```

3. **中文字体问题**
   - 验证工具会自动设置中文字体
   - 如果仍有问题，检查系统字体安装

### 获取帮助

```bash
# 查看命令行帮助
python run_validation.py --help
python validate_coco_dataset.py --help
python binary_mask_validator.py --help
```

## 📖 详细文档

更多详细信息请参考 `VALIDATION_GUIDE.md`。

---

**注意**: 这些验证工具专门为检测和确认AirSim实例分割修复效果而设计，能够有效发现实例合并等问题。
