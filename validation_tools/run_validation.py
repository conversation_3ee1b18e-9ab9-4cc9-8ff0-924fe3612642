#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COCO数据集验证框架

整合所有验证工具，提供完整的验证流程
"""

import os
import sys
import time
import json
import argparse
import subprocess
import yaml
from datetime import datetime
import psutil
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
try:
    import matplotlib.font_manager as fm

    # 查找可用的中文字体
    chinese_fonts = []
    for font in fm.fontManager.ttflist:
        if any(name in font.name.lower() for name in ['pingfang', 'hiragino', 'arial unicode', 'simhei', 'simsun']):
            chinese_fonts.append(font.name)

    if chinese_fonts:
        plt.rcParams['font.sans-serif'] = chinese_fonts[:3] + ['DejaVu Sans']
    else:
        # 如果没有中文字体，使用英文标题
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        USE_ENGLISH_LABELS = True

    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"⚠️ 字体设置失败: {e}")
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    USE_ENGLISH_LABELS = True

# 定义标签映射
LABELS = {
    'memory_title': '验证过程内存使用' if 'USE_ENGLISH_LABELS' not in globals() else 'Memory Usage During Validation',
    'memory_ylabel': '内存使用 (MB)' if 'USE_ENGLISH_LABELS' not in globals() else 'Memory Usage (MB)',
    'cpu_title': '验证过程CPU使用' if 'USE_ENGLISH_LABELS' not in globals() else 'CPU Usage During Validation',
    'cpu_ylabel': 'CPU使用 (%)' if 'USE_ENGLISH_LABELS' not in globals() else 'CPU Usage (%)',
    'time_xlabel': '时间 (秒)' if 'USE_ENGLISH_LABELS' not in globals() else 'Time (seconds)',
    'memory_label': '内存使用' if 'USE_ENGLISH_LABELS' not in globals() else 'Memory Usage',
    'cpu_label': 'CPU使用' if 'USE_ENGLISH_LABELS' not in globals() else 'CPU Usage'
}


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"⚠️ 配置文件加载失败: {e}")
        return {}


def setup_output_directory(output_dir: str) -> str:
    """设置输出目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(output_dir, f"validation_{timestamp}")
    os.makedirs(output_path, exist_ok=True)
    return output_path


def run_coco_validation(coco_path: str, images_dir: str, csv_path: str, 
                       config_path: str, output_dir: str) -> dict:
    """运行COCO格式验证"""
    print("\n" + "="*60)
    print("🔍 运行COCO格式验证...")
    print("="*60)
    
    start_time = time.time()
    
    # 构建命令
    cmd = [
        "python", "validate_coco_dataset.py",
        "--coco", coco_path,
        "--output", output_dir
    ]
    
    if images_dir:
        cmd.extend(["--images", images_dir])
    
    if csv_path:
        cmd.extend(["--csv", csv_path])
    
    if config_path:
        cmd.extend(["--config", config_path])
    
    # 执行命令
    try:
        process = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(process.stdout)
        
        # 加载结果
        result_path = os.path.join(output_dir, "validation_report.json")
        if os.path.exists(result_path):
            with open(result_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
        else:
            results = {"status": "error", "message": "验证报告未生成"}
        
    except subprocess.CalledProcessError as e:
        print(f"❌ COCO验证失败: {e}")
        print(e.stdout)
        print(e.stderr)
        results = {"status": "error", "message": str(e)}
    
    elapsed_time = time.time() - start_time
    print(f"⏱️ COCO验证耗时: {elapsed_time:.2f} 秒")
    
    return results


def run_binary_mask_validation(coco_path: str, images_dir: str, output_dir: str) -> dict:
    """运行二值掩码验证"""
    print("\n" + "="*60)
    print("🎭 运行二值掩码验证...")
    print("="*60)
    
    if not images_dir:
        print("⚠️ 未提供图像目录，跳过二值掩码验证")
        return {"status": "skipped", "message": "未提供图像目录"}
    
    start_time = time.time()
    
    # 构建命令
    mask_output_dir = os.path.join(output_dir, "mask_validation")
    cmd = [
        "python", "binary_mask_validator.py",
        "--coco", coco_path,
        "--images", images_dir,
        "--output", mask_output_dir
    ]
    
    # 执行命令
    try:
        process = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(process.stdout)
        
        # 加载结果
        result_path = os.path.join(mask_output_dir, "mask_validation_results.json")
        if os.path.exists(result_path):
            with open(result_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
        else:
            results = {"status": "error", "message": "掩码验证报告未生成"}
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 二值掩码验证失败: {e}")
        print(e.stdout)
        print(e.stderr)
        results = {"status": "error", "message": str(e)}
    
    elapsed_time = time.time() - start_time
    print(f"⏱️ 二值掩码验证耗时: {elapsed_time:.2f} 秒")
    
    return results


def monitor_performance(output_dir: str) -> dict:
    """监控性能指标"""
    process = psutil.Process(os.getpid())
    
    # 初始内存使用
    initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
    
    # 性能数据
    performance_data = {
        'timestamps': [],
        'memory_usage': [],
        'cpu_percent': []
    }
    
    def record_metrics():
        """记录当前性能指标"""
        memory = process.memory_info().rss / (1024 * 1024)  # MB
        cpu = process.cpu_percent()
        timestamp = time.time()
        
        performance_data['timestamps'].append(timestamp)
        performance_data['memory_usage'].append(memory)
        performance_data['cpu_percent'].append(cpu)
    
    # 启动监控线程
    import threading
    import time
    
    stop_monitoring = threading.Event()
    
    def monitoring_thread():
        while not stop_monitoring.is_set():
            record_metrics()
            time.sleep(1)
    
    monitor = threading.Thread(target=monitoring_thread)
    monitor.daemon = True
    monitor.start()
    
    # 返回停止函数和数据
    def stop_and_get_data():
        stop_monitoring.set()
        monitor.join()
        
        # 计算统计信息
        if performance_data['memory_usage']:
            performance_data['max_memory'] = max(performance_data['memory_usage'])
            performance_data['avg_memory'] = np.mean(performance_data['memory_usage'])
            performance_data['memory_increase'] = performance_data['max_memory'] - initial_memory
        
        if performance_data['cpu_percent']:
            performance_data['max_cpu'] = max(performance_data['cpu_percent'])
            performance_data['avg_cpu'] = np.mean(performance_data['cpu_percent'])
        
        # 生成性能图表
        if len(performance_data['timestamps']) > 1:
            create_performance_charts(performance_data, output_dir)
        
        return performance_data
    
    return stop_and_get_data


def create_performance_charts(performance_data: dict, output_dir: str) -> None:
    """创建性能图表"""
    # 调整时间轴
    start_time = performance_data['timestamps'][0]
    relative_times = [(t - start_time) for t in performance_data['timestamps']]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
    
    # 内存使用图
    ax1.plot(relative_times, performance_data['memory_usage'], 'b-', label=LABELS['memory_label'])
    ax1.set_ylabel(LABELS['memory_ylabel'])
    ax1.set_title(LABELS['memory_title'])
    ax1.grid(True)

    # CPU使用图
    ax2.plot(relative_times, performance_data['cpu_percent'], 'r-', label=LABELS['cpu_label'])
    ax2.set_xlabel(LABELS['time_xlabel'])
    ax2.set_ylabel(LABELS['cpu_ylabel'])
    ax2.set_title(LABELS['cpu_title'])
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'performance_metrics.png'), dpi=150)
    plt.close()


def generate_summary_report(coco_results: dict, mask_results: dict, 
                           performance_data: dict, output_dir: str) -> None:
    """生成总结报告"""
    print("\n" + "="*60)
    print("📋 生成验证总结报告...")
    print("="*60)
    
    # 收集结果
    summary = {
        'timestamp': datetime.now().isoformat(),
        'coco_validation': coco_results,
        'mask_validation': mask_results,
        'performance': performance_data
    }
    
    # 保存JSON报告
    summary_path = os.path.join(output_dir, "validation_summary.json")
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    # 生成HTML报告
    html_path = os.path.join(output_dir, "validation_summary.html")
    generate_html_report(summary, html_path)
    
    print(f"✅ 总结报告已保存: {summary_path}")
    print(f"✅ HTML报告已保存: {html_path}")


def generate_html_report(summary: dict, output_path: str) -> None:
    """生成HTML格式的总结报告"""
    # 提取COCO验证结果
    coco_results = summary.get('coco_validation', {})
    coco_errors = len(coco_results.get('errors', []))
    coco_warnings = len(coco_results.get('warnings', []))
    
    # 提取掩码验证结果
    mask_results = summary.get('mask_validation', {})
    mask_success_rate = mask_results.get('success_rate', 0) * 100
    
    # 提取性能数据
    perf_data = summary.get('performance', {})
    max_memory = perf_data.get('max_memory', 0)
    avg_cpu = perf_data.get('avg_cpu', 0)
    
    # 生成HTML
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COCO数据集验证总结报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .error {{ color: red; }}
        .warning {{ color: orange; }}
        .success {{ color: green; }}
        .metrics {{ display: flex; flex-wrap: wrap; }}
        .metric-card {{ background-color: #f9f9f9; margin: 10px; padding: 15px; border-radius: 5px; width: 200px; }}
        .metric-value {{ font-size: 24px; font-weight: bold; margin: 10px 0; }}
        img {{ max-width: 100%; height: auto; margin: 10px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>COCO数据集验证总结报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>验证概要</h2>
        <div class="metrics">
            <div class="metric-card">
                <h3>COCO格式错误</h3>
                <div class="metric-value {('error' if coco_errors > 0 else 'success')}">{coco_errors}</div>
            </div>
            <div class="metric-card">
                <h3>COCO格式警告</h3>
                <div class="metric-value {('warning' if coco_warnings > 0 else 'success')}">{coco_warnings}</div>
            </div>
            <div class="metric-card">
                <h3>掩码重建成功率</h3>
                <div class="metric-value {('success' if mask_success_rate >= 90 else 'warning')}">{mask_success_rate:.1f}%</div>
            </div>
            <div class="metric-card">
                <h3>最大内存使用</h3>
                <div class="metric-value">{max_memory:.1f} MB</div>
            </div>
            <div class="metric-card">
                <h3>平均CPU使用</h3>
                <div class="metric-value">{avg_cpu:.1f}%</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>性能监控</h2>
        <img src="performance_metrics.png" alt="性能监控图表">
    </div>
    
    <div class="section">
        <h2>验证结果</h2>
        <p>详细结果请查看以下文件:</p>
        <ul>
            <li>COCO验证: <code>validation_report.json</code> 和 <code>validation_report.html</code></li>
            <li>掩码验证: <code>mask_validation/mask_validation_results.json</code></li>
            <li>可视化结果: <code>visualizations/</code> 和 <code>mask_validation/</code> 目录</li>
        </ul>
    </div>
</body>
</html>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='COCO数据集验证框架')
    parser.add_argument('--coco', required=True, help='COCO JSON文件路径')
    parser.add_argument('--images', help='图像目录路径')
    parser.add_argument('--csv', help='原始CSV文件路径')
    parser.add_argument('--config', default='validation_config.yaml', help='验证配置文件路径')
    parser.add_argument('--output', default='validation_results', help='输出目录')
    parser.add_argument('--skip-mask', action='store_true', help='跳过二值掩码验证')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.coco):
        print(f"❌ COCO文件不存在: {args.coco}")
        sys.exit(1)
    
    # 设置输出目录
    output_dir = setup_output_directory(args.output)
    print(f"📂 输出目录: {output_dir}")
    
    # 开始性能监控
    stop_monitoring = monitor_performance(output_dir)
    
    try:
        # 运行COCO格式验证
        coco_results = run_coco_validation(
            args.coco, args.images, args.csv, args.config, output_dir
        )
        
        # 运行二值掩码验证
        mask_results = {}
        if not args.skip_mask and args.images:
            mask_results = run_binary_mask_validation(
                args.coco, args.images, output_dir
            )
        
        # 停止性能监控并获取数据
        performance_data = stop_monitoring()
        
        # 生成总结报告
        generate_summary_report(coco_results, mask_results, performance_data, output_dir)
        
        # 打印总结
        print("\n" + "="*60)
        print("🎉 验证完成!")
        print("="*60)
        print(f"📂 所有验证结果已保存到: {output_dir}")
        
        # 返回适当的退出码
        if coco_results.get('errors', []):
            print(f"❌ 验证失败: 发现 {len(coco_results['errors'])} 个错误")
            sys.exit(1)
        else:
            print(f"✅ 验证通过!")
            sys.exit(0)
            
    except Exception as e:
        print(f"\n💥 验证过程失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 确保停止监控
        stop_monitoring()
        sys.exit(1)


if __name__ == "__main__":
    main()
