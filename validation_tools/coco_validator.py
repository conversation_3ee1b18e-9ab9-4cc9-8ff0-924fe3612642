#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COCO数据集验证器

全面验证COCO格式数据集的规范性、完整性和实例分割特异性
"""

import json
import os
import cv2
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon
from collections import defaultdict
import warnings

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')


class COCOValidator:
    """COCO数据集验证器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化验证器
        
        Args:
            config: 验证配置参数
        """
        self.config = config or self._get_default_config()
        self.validation_results = {
            'errors': [],
            'warnings': [],
            'info': [],
            'statistics': {},
            'validation_time': None
        }
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认验证配置"""
        return {
            'strict_mode': True,              # 严格模式
            'check_geometry': True,           # 检查几何精度
            'generate_visualizations': True,  # 生成可视化
            'max_polygon_points': 1000,       # 多边形最大点数
            'min_area_threshold': 10,         # 最小面积阈值
            'bbox_tolerance': 1.0,            # 边界框容差
            'iou_threshold': 0.8,             # IoU阈值
            'output_dir': 'validation_output' # 输出目录
        }
    
    def validate_coco_dataset(self, coco_path: str, images_dir: str = None, 
                            csv_path: str = None) -> Dict[str, Any]:
        """
        验证COCO数据集
        
        Args:
            coco_path: COCO JSON文件路径
            images_dir: 图像目录路径
            csv_path: 原始CSV文件路径（用于实例分割验证）
            
        Returns:
            验证结果字典
        """
        start_time = datetime.now()
        
        try:
            # 1. 加载COCO数据
            coco_data = self._load_coco_data(coco_path)
            
            # 2. COCO格式规范验证
            self._validate_coco_format(coco_data)
            
            # 3. 数据关联性验证
            self._validate_data_consistency(coco_data)
            
            # 4. 实例分割特异性验证
            if csv_path:
                self._validate_instance_segmentation(coco_data, csv_path)
            
            # 5. 几何精度验证
            if self.config['check_geometry'] and images_dir:
                self._validate_geometry(coco_data, images_dir)
            
            # 6. 生成统计信息
            self._generate_statistics(coco_data)
            
            # 7. 生成可视化
            if self.config['generate_visualizations'] and images_dir:
                self._generate_visualizations(coco_data, images_dir)
            
            # 8. 生成验证报告
            self._generate_validation_report()
            
        except Exception as e:
            self._add_error(f"验证过程失败: {str(e)}")
        
        finally:
            self.validation_results['validation_time'] = (
                datetime.now() - start_time
            ).total_seconds()
        
        return self.validation_results
    
    def _load_coco_data(self, coco_path: str) -> Dict[str, Any]:
        """加载COCO数据"""
        try:
            with open(coco_path, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
            
            self._add_info(f"成功加载COCO文件: {coco_path}")
            return coco_data
            
        except FileNotFoundError:
            self._add_error(f"COCO文件不存在: {coco_path}")
            raise
        except json.JSONDecodeError as e:
            self._add_error(f"COCO文件JSON格式错误: {str(e)}")
            raise
        except Exception as e:
            self._add_error(f"加载COCO文件失败: {str(e)}")
            raise
    
    def _validate_coco_format(self, coco_data: Dict[str, Any]) -> None:
        """验证COCO格式规范"""
        self._add_info("开始COCO格式规范验证...")
        
        # 检查必需的顶级字段
        required_fields = ['info', 'licenses', 'images', 'annotations', 'categories']
        for field in required_fields:
            if field not in coco_data:
                self._add_error(f"缺少必需字段: {field}")
            elif not isinstance(coco_data[field], (list, dict)):
                self._add_error(f"字段 {field} 类型错误，应为list或dict")
        
        # 验证info字段
        self._validate_info_field(coco_data.get('info', {}))
        
        # 验证licenses字段
        self._validate_licenses_field(coco_data.get('licenses', []))
        
        # 验证images字段
        self._validate_images_field(coco_data.get('images', []))
        
        # 验证categories字段
        self._validate_categories_field(coco_data.get('categories', []))
        
        # 验证annotations字段
        self._validate_annotations_field(coco_data.get('annotations', []))
    
    def _validate_info_field(self, info: Dict[str, Any]) -> None:
        """验证info字段"""
        required_info_fields = ['description', 'version', 'year', 'contributor', 'date_created']
        
        for field in required_info_fields:
            if field not in info:
                self._add_warning(f"info字段缺少推荐字段: {field}")
        
        # 验证年份
        if 'year' in info:
            try:
                year = int(info['year'])
                if year < 1900 or year > 2100:
                    self._add_warning(f"年份值异常: {year}")
            except (ValueError, TypeError):
                self._add_error("年份字段应为整数")
    
    def _validate_licenses_field(self, licenses: List[Dict[str, Any]]) -> None:
        """验证licenses字段"""
        if not licenses:
            self._add_warning("licenses字段为空")
            return
        
        for i, license_info in enumerate(licenses):
            if 'id' not in license_info:
                self._add_error(f"license[{i}]缺少id字段")
            if 'name' not in license_info:
                self._add_error(f"license[{i}]缺少name字段")
    
    def _validate_images_field(self, images: List[Dict[str, Any]]) -> None:
        """验证images字段"""
        if not images:
            self._add_error("images字段为空")
            return
        
        image_ids = set()
        required_image_fields = ['id', 'file_name', 'width', 'height']
        
        for i, image in enumerate(images):
            # 检查必需字段
            for field in required_image_fields:
                if field not in image:
                    self._add_error(f"image[{i}]缺少必需字段: {field}")
            
            # 检查ID唯一性
            if 'id' in image:
                if image['id'] in image_ids:
                    self._add_error(f"重复的图像ID: {image['id']}")
                image_ids.add(image['id'])
            
            # 检查尺寸
            if 'width' in image and 'height' in image:
                try:
                    width, height = int(image['width']), int(image['height'])
                    if width <= 0 or height <= 0:
                        self._add_error(f"图像尺寸无效: {width}x{height}")
                except (ValueError, TypeError):
                    self._add_error(f"图像尺寸应为正整数")
    
    def _validate_categories_field(self, categories: List[Dict[str, Any]]) -> None:
        """验证categories字段"""
        if not categories:
            self._add_error("categories字段为空")
            return
        
        category_ids = set()
        category_names = set()
        required_category_fields = ['id', 'name']
        
        for i, category in enumerate(categories):
            # 检查必需字段
            for field in required_category_fields:
                if field not in category:
                    self._add_error(f"category[{i}]缺少必需字段: {field}")
            
            # 检查ID和名称唯一性
            if 'id' in category:
                if category['id'] in category_ids:
                    self._add_error(f"重复的类别ID: {category['id']}")
                category_ids.add(category['id'])
            
            if 'name' in category:
                if category['name'] in category_names:
                    self._add_error(f"重复的类别名称: {category['name']}")
                category_names.add(category['name'])
    
    def _validate_annotations_field(self, annotations: List[Dict[str, Any]]) -> None:
        """验证annotations字段"""
        if not annotations:
            self._add_warning("annotations字段为空")
            return
        
        annotation_ids = set()
        required_annotation_fields = ['id', 'image_id', 'category_id', 'bbox', 'area', 'segmentation', 'iscrowd']
        
        for i, annotation in enumerate(annotations):
            # 检查必需字段
            for field in required_annotation_fields:
                if field not in annotation:
                    self._add_error(f"annotation[{i}]缺少必需字段: {field}")
            
            # 检查ID唯一性
            if 'id' in annotation:
                if annotation['id'] in annotation_ids:
                    self._add_error(f"重复的标注ID: {annotation['id']}")
                annotation_ids.add(annotation['id'])
            
            # 验证bbox格式
            self._validate_bbox(annotation.get('bbox'), i)
            
            # 验证segmentation格式
            self._validate_segmentation(annotation.get('segmentation'), i)
            
            # 验证面积
            self._validate_area(annotation.get('area'), i)
            
            # 验证实例分割特有字段
            self._validate_instance_fields(annotation, i)
    
    def _validate_bbox(self, bbox: Any, annotation_idx: int) -> None:
        """验证边界框格式"""
        if not isinstance(bbox, list) or len(bbox) != 4:
            self._add_error(f"annotation[{annotation_idx}] bbox格式错误，应为[x,y,width,height]")
            return
        
        try:
            x, y, w, h = [float(v) for v in bbox]
            if w <= 0 or h <= 0:
                self._add_error(f"annotation[{annotation_idx}] bbox尺寸无效: width={w}, height={h}")
            if x < 0 or y < 0:
                self._add_warning(f"annotation[{annotation_idx}] bbox坐标为负值: x={x}, y={y}")
        except (ValueError, TypeError):
            self._add_error(f"annotation[{annotation_idx}] bbox坐标应为数值")
    
    def _validate_segmentation(self, segmentation: Any, annotation_idx: int) -> None:
        """验证分割格式"""
        if not isinstance(segmentation, list):
            self._add_error(f"annotation[{annotation_idx}] segmentation应为list")
            return
        
        if len(segmentation) == 0:
            self._add_error(f"annotation[{annotation_idx}] segmentation为空")
            return
        
        # 检查多边形格式
        for i, polygon in enumerate(segmentation):
            if not isinstance(polygon, list):
                self._add_error(f"annotation[{annotation_idx}] segmentation[{i}]应为坐标列表")
                continue
            
            if len(polygon) < 6:  # 至少3个点，每个点2个坐标
                self._add_error(f"annotation[{annotation_idx}] segmentation[{i}]点数不足")
                continue
            
            if len(polygon) % 2 != 0:
                self._add_error(f"annotation[{annotation_idx}] segmentation[{i}]坐标数应为偶数")
                continue
            
            # 检查坐标值
            try:
                coords = [float(v) for v in polygon]
                if any(c < 0 for c in coords):
                    self._add_warning(f"annotation[{annotation_idx}] segmentation[{i}]包含负坐标")
            except (ValueError, TypeError):
                self._add_error(f"annotation[{annotation_idx}] segmentation[{i}]坐标应为数值")
    
    def _validate_area(self, area: Any, annotation_idx: int) -> None:
        """验证面积"""
        try:
            area_val = float(area)
            if area_val <= 0:
                self._add_error(f"annotation[{annotation_idx}] area应为正数: {area_val}")
            elif area_val < self.config['min_area_threshold']:
                self._add_warning(f"annotation[{annotation_idx}] area过小: {area_val}")
        except (ValueError, TypeError):
            self._add_error(f"annotation[{annotation_idx}] area应为数值")
    
    def _validate_instance_fields(self, annotation: Dict[str, Any], annotation_idx: int) -> None:
        """验证实例分割特有字段"""
        # 检查instance_name字段
        if 'instance_name' in annotation:
            if not isinstance(annotation['instance_name'], str):
                self._add_error(f"annotation[{annotation_idx}] instance_name应为字符串")
            elif not annotation['instance_name'].strip():
                self._add_warning(f"annotation[{annotation_idx}] instance_name为空")
        
        # 检查instance_color字段
        if 'instance_color' in annotation:
            color = annotation['instance_color']
            if not isinstance(color, list) or len(color) != 3:
                self._add_error(f"annotation[{annotation_idx}] instance_color应为RGB三元组")
            else:
                try:
                    r, g, b = [int(v) for v in color]
                    if not all(0 <= v <= 255 for v in [r, g, b]):
                        self._add_error(f"annotation[{annotation_idx}] instance_color值超出范围[0,255]: {color}")
                except (ValueError, TypeError):
                    self._add_error(f"annotation[{annotation_idx}] instance_color应为整数")
    
    def _add_error(self, message: str) -> None:
        """添加错误信息"""
        self.validation_results['errors'].append({
            'timestamp': datetime.now().isoformat(),
            'message': message
        })
    
    def _add_warning(self, message: str) -> None:
        """添加警告信息"""
        self.validation_results['warnings'].append({
            'timestamp': datetime.now().isoformat(),
            'message': message
        })
    
    def _add_info(self, message: str) -> None:
        """添加信息"""
        self.validation_results['info'].append({
            'timestamp': datetime.now().isoformat(),
            'message': message
        })

    def _validate_data_consistency(self, coco_data: Dict[str, Any]) -> None:
        """验证数据关联性和完整性"""
        self._add_info("开始数据关联性验证...")

        images = coco_data.get('images', [])
        annotations = coco_data.get('annotations', [])
        categories = coco_data.get('categories', [])

        # 构建ID映射
        image_ids = {img['id'] for img in images if 'id' in img}
        category_ids = {cat['id'] for cat in categories if 'id' in cat}

        # 验证annotations的关联性
        for i, annotation in enumerate(annotations):
            # 验证image_id关联
            if 'image_id' in annotation:
                if annotation['image_id'] not in image_ids:
                    self._add_error(f"annotation[{i}] image_id={annotation['image_id']} 不存在对应的图像")

            # 验证category_id关联
            if 'category_id' in annotation:
                if annotation['category_id'] not in category_ids:
                    self._add_error(f"annotation[{i}] category_id={annotation['category_id']} 不存在对应的类别")

            # 验证bbox是否在图像范围内
            if 'image_id' in annotation and 'bbox' in annotation:
                self._validate_bbox_in_image(annotation, images, i)

    def _validate_bbox_in_image(self, annotation: Dict[str, Any],
                               images: List[Dict[str, Any]], annotation_idx: int) -> None:
        """验证边界框是否在图像范围内"""
        image_id = annotation['image_id']
        bbox = annotation['bbox']

        # 找到对应的图像
        image = None
        for img in images:
            if img.get('id') == image_id:
                image = img
                break

        if not image or 'width' not in image or 'height' not in image:
            return

        try:
            x, y, w, h = [float(v) for v in bbox]
            img_width, img_height = int(image['width']), int(image['height'])

            # 检查边界框是否超出图像边界
            if x < 0 or y < 0:
                self._add_warning(f"annotation[{annotation_idx}] bbox起点超出图像边界: ({x}, {y})")

            if x + w > img_width or y + h > img_height:
                self._add_warning(f"annotation[{annotation_idx}] bbox超出图像边界: "
                                f"bbox=({x}, {y}, {w}, {h}), image_size=({img_width}, {img_height})")
        except (ValueError, TypeError, KeyError):
            pass  # 已在其他地方验证过格式

    def _validate_instance_segmentation(self, coco_data: Dict[str, Any], csv_path: str) -> None:
        """验证实例分割特异性"""
        self._add_info("开始实例分割特异性验证...")

        try:
            # 加载原始CSV数据
            csv_data = pd.read_csv(csv_path, encoding='utf-8')

            # 分析CSV中的实例
            csv_instances = self._analyze_csv_instances(csv_data)

            # 分析COCO中的实例
            coco_instances = self._analyze_coco_instances(coco_data)

            # 对比验证
            self._compare_instances(csv_instances, coco_instances)

        except Exception as e:
            self._add_error(f"实例分割验证失败: {str(e)}")

    def _analyze_csv_instances(self, csv_data: pd.DataFrame) -> Dict[str, Any]:
        """分析CSV中的实例"""
        instances = {}

        for _, row in csv_data.iterrows():
            object_name = row['ObjectName']
            color = (int(row['R']), int(row['G']), int(row['B']))

            instances[color] = {
                'object_name': object_name,
                'color': color,
                'base_category': object_name.split('_')[0].lower()
            }

        return instances

    def _analyze_coco_instances(self, coco_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析COCO中的实例"""
        annotations = coco_data.get('annotations', [])
        categories = coco_data.get('categories', [])

        # 构建类别映射
        category_map = {cat['id']: cat['name'] for cat in categories}

        instances = {}

        for annotation in annotations:
            if 'instance_color' in annotation and 'instance_name' in annotation:
                color = tuple(annotation['instance_color'])

                instances[color] = {
                    'annotation_id': annotation['id'],
                    'object_name': annotation['instance_name'],
                    'category_id': annotation['category_id'],
                    'category_name': category_map.get(annotation['category_id'], 'unknown'),
                    'color': color,
                    'bbox': annotation.get('bbox'),
                    'area': annotation.get('area')
                }

        return instances

    def _compare_instances(self, csv_instances: Dict[str, Any],
                          coco_instances: Dict[str, Any]) -> None:
        """对比CSV和COCO中的实例"""
        csv_colors = set(csv_instances.keys())
        coco_colors = set(coco_instances.keys())

        # 检查缺失的实例
        missing_in_coco = csv_colors - coco_colors
        extra_in_coco = coco_colors - csv_colors

        if missing_in_coco:
            for color in missing_in_coco:
                csv_obj = csv_instances[color]
                self._add_error(f"CSV实例未转换到COCO: {csv_obj['object_name']} RGB{color}")

        if extra_in_coco:
            for color in extra_in_coco:
                coco_obj = coco_instances[color]
                self._add_warning(f"COCO中存在额外实例: {coco_obj['object_name']} RGB{color}")

        # 检查匹配实例的一致性
        common_colors = csv_colors & coco_colors
        for color in common_colors:
            csv_obj = csv_instances[color]
            coco_obj = coco_instances[color]

            if csv_obj['object_name'] != coco_obj['object_name']:
                self._add_error(f"实例名称不匹配 RGB{color}: "
                              f"CSV={csv_obj['object_name']}, COCO={coco_obj['object_name']}")

        # 统计信息
        self.validation_results['statistics']['csv_instances'] = len(csv_instances)
        self.validation_results['statistics']['coco_instances'] = len(coco_instances)
        self.validation_results['statistics']['matched_instances'] = len(common_colors)
        self.validation_results['statistics']['missing_instances'] = len(missing_in_coco)
        self.validation_results['statistics']['extra_instances'] = len(extra_in_coco)

    def _validate_geometry(self, coco_data: Dict[str, Any], images_dir: str) -> None:
        """验证几何精度"""
        self._add_info("开始几何精度验证...")

        images = coco_data.get('images', [])
        annotations = coco_data.get('annotations', [])

        geometry_stats = {
            'total_annotations': len(annotations),
            'bbox_accuracy_issues': 0,
            'segmentation_issues': 0,
            'area_mismatches': 0
        }

        for annotation in annotations:
            try:
                # 找到对应的图像
                image = self._find_image_by_id(images, annotation.get('image_id'))
                if not image:
                    continue

                image_path = os.path.join(images_dir, image['file_name'])
                if not os.path.exists(image_path):
                    self._add_warning(f"图像文件不存在: {image['file_name']}")
                    continue

                # 验证几何精度
                self._validate_annotation_geometry(annotation, image_path, geometry_stats)

            except Exception as e:
                self._add_warning(f"几何验证失败 annotation_id={annotation.get('id')}: {str(e)}")

        self.validation_results['statistics']['geometry'] = geometry_stats

    def _find_image_by_id(self, images: List[Dict[str, Any]], image_id: int) -> Optional[Dict[str, Any]]:
        """根据ID查找图像"""
        for image in images:
            if image.get('id') == image_id:
                return image
        return None

    def _validate_annotation_geometry(self, annotation: Dict[str, Any],
                                    image_path: str, stats: Dict[str, int]) -> None:
        """验证单个标注的几何精度"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            return

        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 获取实例颜色
        if 'instance_color' not in annotation:
            return

        target_color = tuple(annotation['instance_color'])

        # 创建颜色掩码
        color_mask = np.all(image_rgb == target_color, axis=2)

        if not np.any(color_mask):
            self._add_warning(f"annotation_id={annotation['id']} 在图像中未找到对应颜色 RGB{target_color}")
            return

        # 验证边界框精度
        self._validate_bbox_accuracy(annotation, color_mask, stats)

        # 验证分割精度
        self._validate_segmentation_accuracy(annotation, color_mask, stats)

        # 验证面积精度
        self._validate_area_accuracy(annotation, color_mask, stats)

    def _validate_bbox_accuracy(self, annotation: Dict[str, Any],
                               color_mask: np.ndarray, stats: Dict[str, int]) -> None:
        """验证边界框精度"""
        if 'bbox' not in annotation:
            return

        try:
            # COCO边界框
            x, y, w, h = [float(v) for v in annotation['bbox']]
            coco_bbox = [int(x), int(y), int(x + w), int(y + h)]

            # 实际边界框
            coords = np.where(color_mask)
            if len(coords[0]) == 0:
                return

            actual_bbox = [
                int(np.min(coords[1])),  # min_x
                int(np.min(coords[0])),  # min_y
                int(np.max(coords[1])),  # max_x
                int(np.max(coords[0]))   # max_y
            ]

            # 计算IoU
            iou = self._calculate_bbox_iou(coco_bbox, actual_bbox)

            if iou < self.config['iou_threshold']:
                self._add_warning(f"annotation_id={annotation['id']} bbox精度不足: IoU={iou:.3f}")
                stats['bbox_accuracy_issues'] += 1

        except Exception as e:
            self._add_warning(f"bbox精度验证失败: {str(e)}")

    def _calculate_bbox_iou(self, bbox1: List[int], bbox2: List[int]) -> float:
        """计算两个边界框的IoU"""
        x1_min, y1_min, x1_max, y1_max = bbox1
        x2_min, y2_min, x2_max, y2_max = bbox2

        # 计算交集
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)

        if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
            return 0.0

        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)

        # 计算并集
        area1 = (x1_max - x1_min) * (y1_max - y1_min)
        area2 = (x2_max - x2_min) * (y2_max - y2_min)
        union_area = area1 + area2 - inter_area

        return inter_area / union_area if union_area > 0 else 0.0

    def _validate_segmentation_accuracy(self, annotation: Dict[str, Any],
                                      color_mask: np.ndarray, stats: Dict[str, int]) -> None:
        """验证分割精度"""
        if 'segmentation' not in annotation:
            return

        try:
            # 从COCO分割重建掩码
            segmentation = annotation['segmentation']
            height, width = color_mask.shape

            reconstructed_mask = np.zeros((height, width), dtype=np.uint8)

            for polygon in segmentation:
                if len(polygon) >= 6:  # 至少3个点
                    points = np.array(polygon).reshape(-1, 2).astype(np.int32)
                    cv2.fillPoly(reconstructed_mask, [points], 1)

            # 计算掩码IoU
            intersection = np.logical_and(color_mask, reconstructed_mask).sum()
            union = np.logical_or(color_mask, reconstructed_mask).sum()

            iou = intersection / union if union > 0 else 0.0

            if iou < self.config['iou_threshold']:
                self._add_warning(f"annotation_id={annotation['id']} 分割精度不足: IoU={iou:.3f}")
                stats['segmentation_issues'] += 1

        except Exception as e:
            self._add_warning(f"分割精度验证失败: {str(e)}")

    def _validate_area_accuracy(self, annotation: Dict[str, Any],
                               color_mask: np.ndarray, stats: Dict[str, int]) -> None:
        """验证面积精度"""
        if 'area' not in annotation:
            return

        try:
            coco_area = float(annotation['area'])
            actual_area = float(np.sum(color_mask))

            # 计算面积差异百分比
            area_diff = abs(coco_area - actual_area) / actual_area * 100

            if area_diff > 10:  # 超过10%差异
                self._add_warning(f"annotation_id={annotation['id']} 面积差异过大: "
                                f"COCO={coco_area:.1f}, 实际={actual_area:.1f}, 差异={area_diff:.1f}%")
                stats['area_mismatches'] += 1

        except Exception as e:
            self._add_warning(f"面积精度验证失败: {str(e)}")

    def _generate_statistics(self, coco_data: Dict[str, Any]) -> None:
        """生成统计信息"""
        images = coco_data.get('images', [])
        annotations = coco_data.get('annotations', [])
        categories = coco_data.get('categories', [])

        # 基本统计
        basic_stats = {
            'total_images': len(images),
            'total_annotations': len(annotations),
            'total_categories': len(categories),
            'avg_annotations_per_image': len(annotations) / len(images) if images else 0
        }

        # 类别统计
        category_stats = defaultdict(int)
        area_stats = []

        for annotation in annotations:
            if 'category_id' in annotation:
                category_stats[annotation['category_id']] += 1
            if 'area' in annotation:
                area_stats.append(annotation['area'])

        # 面积统计
        if area_stats:
            area_summary = {
                'min_area': min(area_stats),
                'max_area': max(area_stats),
                'mean_area': np.mean(area_stats),
                'median_area': np.median(area_stats)
            }
        else:
            area_summary = {}

        self.validation_results['statistics'].update({
            'basic': basic_stats,
            'categories': dict(category_stats),
            'areas': area_summary
        })

    def _generate_visualizations(self, coco_data: Dict[str, Any], images_dir: str) -> None:
        """生成可视化"""
        self._add_info("生成可视化图像...")

        # 创建输出目录
        viz_dir = os.path.join(self.config['output_dir'], 'visualizations')
        os.makedirs(viz_dir, exist_ok=True)

        images = coco_data.get('images', [])
        annotations = coco_data.get('annotations', [])
        categories = coco_data.get('categories', [])

        # 构建类别映射
        category_map = {cat['id']: cat['name'] for cat in categories}

        # 为每张图像生成可视化
        for image in images[:5]:  # 限制前5张图像
            try:
                self._create_image_visualization(
                    image, annotations, category_map, images_dir, viz_dir
                )
            except Exception as e:
                self._add_warning(f"可视化生成失败 {image.get('file_name')}: {str(e)}")

        # 生成统计图表
        self._create_statistics_charts(coco_data, viz_dir)

    def _create_image_visualization(self, image: Dict[str, Any],
                                   annotations: List[Dict[str, Any]],
                                   category_map: Dict[int, str],
                                   images_dir: str, viz_dir: str) -> None:
        """为单张图像创建可视化"""
        image_path = os.path.join(images_dir, image['file_name'])
        if not os.path.exists(image_path):
            return

        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            return

        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # 找到该图像的所有标注
        image_annotations = [ann for ann in annotations if ann.get('image_id') == image['id']]

        if not image_annotations:
            return

        # 创建可视化
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        # 原始图像
        axes[0].imshow(img_rgb)
        axes[0].set_title('原始图像', fontsize=14)
        axes[0].axis('off')

        # 边界框可视化
        axes[1].imshow(img_rgb)
        for ann in image_annotations:
            if 'bbox' in ann and 'category_id' in ann:
                x, y, w, h = ann['bbox']
                rect = patches.Rectangle((x, y), w, h, linewidth=2,
                                       edgecolor='red', facecolor='none')
                axes[1].add_patch(rect)

                # 添加标签
                category_name = category_map.get(ann['category_id'], 'unknown')
                axes[1].text(x, y-5, category_name, color='red', fontsize=10, weight='bold')

        axes[1].set_title('边界框标注', fontsize=14)
        axes[1].axis('off')

        # 分割掩码可视化
        axes[2].imshow(img_rgb)
        for ann in image_annotations:
            if 'segmentation' in ann:
                for polygon in ann['segmentation']:
                    if len(polygon) >= 6:
                        points = np.array(polygon).reshape(-1, 2)
                        poly = Polygon(points, alpha=0.5, facecolor='blue', edgecolor='blue')
                        axes[2].add_patch(poly)

        axes[2].set_title('分割掩码', fontsize=14)
        axes[2].axis('off')

        plt.tight_layout()

        # 保存可视化
        output_path = os.path.join(viz_dir, f"validation_{image['file_name']}")
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()

    def _create_statistics_charts(self, coco_data: Dict[str, Any], viz_dir: str) -> None:
        """创建统计图表"""
        try:
            annotations = coco_data.get('annotations', [])
            categories = coco_data.get('categories', [])

            if not annotations or not categories:
                return

            # 类别分布图
            category_counts = defaultdict(int)
            category_map = {cat['id']: cat['name'] for cat in categories}

            for ann in annotations:
                if 'category_id' in ann:
                    category_counts[ann['category_id']] += 1

            if category_counts:
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

                # 类别分布柱状图
                cat_names = [category_map.get(cat_id, f'ID_{cat_id}') for cat_id in category_counts.keys()]
                counts = list(category_counts.values())

                bars = ax1.bar(cat_names, counts, color='skyblue')
                ax1.set_title('类别分布', fontsize=14)
                ax1.set_ylabel('实例数量', fontsize=12)
                ax1.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, count in zip(bars, counts):
                    height = bar.get_height()
                    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                           f'{count}', ha='center', va='bottom')

                # 面积分布直方图
                areas = [ann['area'] for ann in annotations if 'area' in ann]
                if areas:
                    ax2.hist(areas, bins=20, color='lightgreen', alpha=0.7)
                    ax2.set_title('面积分布', fontsize=14)
                    ax2.set_xlabel('面积', fontsize=12)
                    ax2.set_ylabel('频次', fontsize=12)

                plt.tight_layout()
                plt.savefig(os.path.join(viz_dir, 'statistics.png'), dpi=150, bbox_inches='tight')
                plt.close()

        except Exception as e:
            self._add_warning(f"统计图表生成失败: {str(e)}")

    def _generate_validation_report(self) -> None:
        """生成验证报告"""
        self._add_info("生成验证报告...")

        # 创建输出目录
        os.makedirs(self.config['output_dir'], exist_ok=True)

        # 生成JSON报告
        json_report_path = os.path.join(self.config['output_dir'], 'validation_report.json')
        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, ensure_ascii=False, indent=2)

        # 生成HTML报告
        html_report_path = os.path.join(self.config['output_dir'], 'validation_report.html')
        self._generate_html_report(html_report_path)

        self._add_info(f"验证报告已保存: {json_report_path}, {html_report_path}")

    def _generate_html_report(self, output_path: str) -> None:
        """生成HTML格式的验证报告"""
        results = self.validation_results

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COCO数据集验证报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .error {{ color: red; }}
        .warning {{ color: orange; }}
        .info {{ color: blue; }}
        .success {{ color: green; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>COCO数据集验证报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>验证耗时: {results.get('validation_time', 0):.2f} 秒</p>
    </div>

    <div class="section">
        <h2>验证概要</h2>
        <p class="error">错误: {len(results['errors'])} 个</p>
        <p class="warning">警告: {len(results['warnings'])} 个</p>
        <p class="info">信息: {len(results['info'])} 个</p>
    </div>

    <div class="section">
        <h2>统计信息</h2>
        <table>
            <tr><th>项目</th><th>数值</th></tr>
        """

        # 添加统计信息
        stats = results.get('statistics', {})
        for key, value in stats.items():
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    html_content += f"<tr><td>{key}.{sub_key}</td><td>{sub_value}</td></tr>"
            else:
                html_content += f"<tr><td>{key}</td><td>{value}</td></tr>"

        html_content += """
        </table>
    </div>

    <div class="section">
        <h2>详细信息</h2>
        """

        # 添加错误信息
        if results['errors']:
            html_content += "<h3 class='error'>错误</h3><ul>"
            for error in results['errors']:
                html_content += f"<li class='error'>{error['message']}</li>"
            html_content += "</ul>"

        # 添加警告信息
        if results['warnings']:
            html_content += "<h3 class='warning'>警告</h3><ul>"
            for warning in results['warnings']:
                html_content += f"<li class='warning'>{warning['message']}</li>"
            html_content += "</ul>"

        html_content += """
    </div>
</body>
</html>
        """

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
