#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二值掩码验证工具

从COCO分割数据重建二值掩码，并与原始分割图像进行像素级对比验证
支持生成实例级和类别级聚合掩码
"""

import json
import cv2
import numpy as np
import os
import yaml
from typing import Dict, List, Any, Tuple
import matplotlib.pyplot as plt
from PIL import Image
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB']
plt.rcParams['axes.unicode_minus'] = False


class BinaryMaskValidator:
    """二值掩码验证器（支持实例级和类别级掩码）"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        self.validation_results = {
            'total_masks': 0,
            'successful_reconstructions': 0,
            'failed_reconstructions': 0,
            'pixel_accuracy_scores': [],
            'iou_scores': [],
            'detailed_results': [],
            'category_masks': {},
            'instance_masks': {}
        }

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'mask_generation': {
                'generate_instance_masks': True,
                'generate_category_masks': True,
                'generate_comparisons': True,
                'output_structure': {
                    'instance_dir': 'instance_masks',
                    'category_dir': 'category_masks',
                    'comparison_dir': 'comparisons'
                },
                'visualization': {
                    'show_statistics': True,
                    'max_categories_per_row': 4,
                    'figure_dpi': 150
                }
            }
        }
    
    def validate_masks(self, coco_path: str, images_dir: str, output_dir: str = "mask_validation") -> Dict[str, Any]:
        """
        验证COCO分割数据的二值掩码准确性，支持实例级和类别级掩码生成

        Args:
            coco_path: COCO JSON文件路径
            images_dir: 原始分割图像目录
            output_dir: 输出目录

        Returns:
            验证结果字典
        """
        print("🔍 开始二值掩码验证和生成...")

        # 创建输出目录结构
        self._create_output_directories(output_dir)

        # 加载COCO数据
        with open(coco_path, 'r', encoding='utf-8') as f:
            coco_data = json.load(f)

        images = coco_data.get('images', [])
        annotations = coco_data.get('annotations', [])
        categories = coco_data.get('categories', [])

        # 构建类别映射
        category_map = {cat['id']: cat['name'] for cat in categories}

        # 按图像分组标注
        image_annotations = defaultdict(list)
        for ann in annotations:
            image_annotations[ann['image_id']].append(ann)

        # 处理每张图像
        for image in images:
            image_id = image['id']
            if image_id in image_annotations:
                self._process_image_masks(
                    image, image_annotations[image_id], category_map,
                    images_dir, output_dir
                )

        # 计算总体统计
        self._calculate_overall_statistics()

        return self.validation_results

    def _create_output_directories(self, output_dir: str) -> None:
        """创建输出目录结构"""
        config = self.config['mask_generation']

        os.makedirs(output_dir, exist_ok=True)

        if config['generate_instance_masks']:
            instance_dir = os.path.join(output_dir, config['output_structure']['instance_dir'])
            os.makedirs(instance_dir, exist_ok=True)

        if config['generate_category_masks']:
            category_dir = os.path.join(output_dir, config['output_structure']['category_dir'])
            os.makedirs(category_dir, exist_ok=True)

        if config['generate_comparisons']:
            comparison_dir = os.path.join(output_dir, config['output_structure']['comparison_dir'])
            os.makedirs(comparison_dir, exist_ok=True)
    
    def _process_image_masks(self, image: Dict[str, Any], annotations: List[Dict[str, Any]],
                            category_map: Dict[int, str], images_dir: str, output_dir: str) -> None:
        """处理单张图像的掩码生成和验证"""
        image_path = os.path.join(images_dir, image['file_name'])

        if not os.path.exists(image_path):
            print(f"⚠️ 图像文件不存在: {image['file_name']}")
            return

        # 读取原始分割图像
        original_image = cv2.imread(image_path)
        if original_image is None:
            print(f"⚠️ 无法读取图像: {image['file_name']}")
            return

        original_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        height, width = original_rgb.shape[:2]

        print(f"📸 验证图像: {image['file_name']} ({width}x{height})")

        # 按类别分组标注
        category_annotations = defaultdict(list)
        for ann in annotations:
            if 'instance_color' in ann and 'category_id' in ann:
                category_annotations[ann['category_id']].append(ann)

        # 生成和验证实例级掩码
        instance_results = {}
        if self.config['mask_generation']['generate_instance_masks']:
            instance_results = self._generate_and_validate_instance_masks(
                annotations, original_rgb, image['file_name'], output_dir
            )

        # 生成类别级掩码
        category_results = {}
        if self.config['mask_generation']['generate_category_masks']:
            category_results = self._generate_category_masks(
                category_annotations, category_map, original_rgb,
                image['file_name'], output_dir
            )

        # 生成对比可视化
        if self.config['mask_generation']['generate_comparisons']:
            self._generate_comparison_visualization(
                original_rgb, instance_results, category_results, category_map,
                image['file_name'], output_dir
            )

        # 保存结果
        self.validation_results['instance_masks'][image['file_name']] = instance_results
        self.validation_results['category_masks'][image['file_name']] = category_results

    def _validate_image_masks(self, image: Dict[str, Any], annotations: List[Dict[str, Any]],
                             images_dir: str, output_dir: str) -> None:
        """验证单张图像的掩码"""
        image_path = os.path.join(images_dir, image['file_name'])
        
        if not os.path.exists(image_path):
            print(f"⚠️ 图像文件不存在: {image['file_name']}")
            return
        
        # 读取原始分割图像
        original_image = cv2.imread(image_path)
        if original_image is None:
            print(f"⚠️ 无法读取图像: {image['file_name']}")
            return
        
        original_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        height, width = original_rgb.shape[:2]
        
        print(f"📸 验证图像: {image['file_name']} ({width}x{height})")
        
        # 为每个标注验证掩码
        for i, annotation in enumerate(annotations):
            if 'instance_color' not in annotation or 'segmentation' not in annotation:
                continue
            
            result = self._validate_single_mask(
                annotation, original_rgb, width, height, 
                f"{image['file_name']}_ann_{annotation['id']}", output_dir
            )
            
            if result:
                self.validation_results['detailed_results'].append({
                    'image_file': image['file_name'],
                    'annotation_id': annotation['id'],
                    'instance_name': annotation.get('instance_name', 'unknown'),
                    'instance_color': annotation['instance_color'],
                    **result
                })
    
    def _validate_single_mask(self, annotation: Dict[str, Any], original_rgb: np.ndarray,
                             width: int, height: int, mask_name: str, output_dir: str) -> Dict[str, Any]:
        """验证单个掩码"""
        try:
            # 从原始图像提取目标颜色掩码
            target_color = tuple(annotation['instance_color'])
            original_mask = np.all(original_rgb == target_color, axis=2)
            
            if not np.any(original_mask):
                print(f"   ⚠️ 在原始图像中未找到颜色 RGB{target_color}")
                return None
            
            # 从COCO分割重建掩码
            reconstructed_mask = self._reconstruct_mask_from_segmentation(
                annotation['segmentation'], width, height
            )
            
            # 计算精度指标
            metrics = self._calculate_mask_metrics(original_mask, reconstructed_mask)
            
            # 保存对比图像
            self._save_mask_comparison(
                original_mask, reconstructed_mask, mask_name, output_dir, metrics
            )
            
            self.validation_results['total_masks'] += 1
            if metrics['iou'] > 0.5:  # 认为IoU > 0.5为成功重建
                self.validation_results['successful_reconstructions'] += 1
            else:
                self.validation_results['failed_reconstructions'] += 1
            
            self.validation_results['pixel_accuracy_scores'].append(metrics['pixel_accuracy'])
            self.validation_results['iou_scores'].append(metrics['iou'])
            
            print(f"   ✅ {annotation.get('instance_name', 'unknown')}: "
                  f"IoU={metrics['iou']:.3f}, 像素精度={metrics['pixel_accuracy']:.3f}")
            
            return metrics
            
        except Exception as e:
            print(f"   ❌ 掩码验证失败: {str(e)}")
            self.validation_results['failed_reconstructions'] += 1
            return None
    
    def _reconstruct_mask_from_segmentation(self, segmentation: List[List[float]], 
                                           width: int, height: int) -> np.ndarray:
        """从COCO分割数据重建二值掩码"""
        mask = np.zeros((height, width), dtype=np.uint8)
        
        for polygon in segmentation:
            if len(polygon) >= 6:  # 至少3个点
                # 将坐标重塑为点对
                points = np.array(polygon).reshape(-1, 2).astype(np.int32)
                # 填充多边形
                cv2.fillPoly(mask, [points], 1)
        
        return mask.astype(bool)
    
    def _calculate_mask_metrics(self, original_mask: np.ndarray, 
                               reconstructed_mask: np.ndarray) -> Dict[str, float]:
        """计算掩码精度指标"""
        # 确保掩码为布尔类型
        original_mask = original_mask.astype(bool)
        reconstructed_mask = reconstructed_mask.astype(bool)
        
        # 计算交集和并集
        intersection = np.logical_and(original_mask, reconstructed_mask)
        union = np.logical_or(original_mask, reconstructed_mask)
        
        # IoU (Intersection over Union)
        iou = np.sum(intersection) / np.sum(union) if np.sum(union) > 0 else 0.0
        
        # 像素精度 (Pixel Accuracy)
        correct_pixels = np.sum(original_mask == reconstructed_mask)
        total_pixels = original_mask.size
        pixel_accuracy = correct_pixels / total_pixels
        
        # Dice系数
        dice = 2 * np.sum(intersection) / (np.sum(original_mask) + np.sum(reconstructed_mask)) \
               if (np.sum(original_mask) + np.sum(reconstructed_mask)) > 0 else 0.0
        
        # 精确率和召回率
        true_positives = np.sum(intersection)
        false_positives = np.sum(reconstructed_mask) - true_positives
        false_negatives = np.sum(original_mask) - true_positives
        
        precision = true_positives / (true_positives + false_positives) \
                   if (true_positives + false_positives) > 0 else 0.0
        recall = true_positives / (true_positives + false_negatives) \
                if (true_positives + false_negatives) > 0 else 0.0
        
        return {
            'iou': float(iou),
            'pixel_accuracy': float(pixel_accuracy),
            'dice': float(dice),
            'precision': float(precision),
            'recall': float(recall),
            'original_area': int(np.sum(original_mask)),
            'reconstructed_area': int(np.sum(reconstructed_mask))
        }
    
    def _save_mask_comparison(self, original_mask: np.ndarray, reconstructed_mask: np.ndarray,
                             mask_name: str, output_dir: str, metrics: Dict[str, float]) -> None:
        """保存掩码对比图像"""
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        # 原始掩码
        axes[0].imshow(original_mask, cmap='gray')
        axes[0].set_title('原始掩码', fontsize=12)
        axes[0].axis('off')
        
        # 重建掩码
        axes[1].imshow(reconstructed_mask, cmap='gray')
        axes[1].set_title('重建掩码', fontsize=12)
        axes[1].axis('off')
        
        # 差异图
        diff_mask = original_mask.astype(int) - reconstructed_mask.astype(int)
        axes[2].imshow(diff_mask, cmap='RdBu', vmin=-1, vmax=1)
        axes[2].set_title('差异图', fontsize=12)
        axes[2].axis('off')
        
        # 叠加图
        overlay = np.zeros((*original_mask.shape, 3))
        overlay[original_mask, 0] = 1.0  # 原始掩码为红色
        overlay[reconstructed_mask, 1] = 1.0  # 重建掩码为绿色
        # 重叠区域为黄色
        axes[3].imshow(overlay)
        axes[3].set_title('叠加图 (红:原始, 绿:重建)', fontsize=12)
        axes[3].axis('off')
        
        # 添加指标文本
        metrics_text = f"IoU: {metrics['iou']:.3f}\n" \
                      f"像素精度: {metrics['pixel_accuracy']:.3f}\n" \
                      f"Dice: {metrics['dice']:.3f}\n" \
                      f"精确率: {metrics['precision']:.3f}\n" \
                      f"召回率: {metrics['recall']:.3f}"
        
        fig.suptitle(f"{mask_name}\n{metrics_text}", fontsize=10)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"{mask_name}_comparison.png"), 
                   dpi=150, bbox_inches='tight')
        plt.close()
    
    def _calculate_overall_statistics(self) -> None:
        """计算总体统计信息"""
        if self.validation_results['pixel_accuracy_scores']:
            self.validation_results['avg_pixel_accuracy'] = np.mean(
                self.validation_results['pixel_accuracy_scores']
            )
            self.validation_results['min_pixel_accuracy'] = np.min(
                self.validation_results['pixel_accuracy_scores']
            )
            self.validation_results['max_pixel_accuracy'] = np.max(
                self.validation_results['pixel_accuracy_scores']
            )
        
        if self.validation_results['iou_scores']:
            self.validation_results['avg_iou'] = np.mean(
                self.validation_results['iou_scores']
            )
            self.validation_results['min_iou'] = np.min(
                self.validation_results['iou_scores']
            )
            self.validation_results['max_iou'] = np.max(
                self.validation_results['iou_scores']
            )
        
        # 计算成功率
        if self.validation_results['total_masks'] > 0:
            self.validation_results['success_rate'] = (
                self.validation_results['successful_reconstructions'] / 
                self.validation_results['total_masks']
            )
    
    def print_summary(self) -> None:
        """打印验证摘要"""
        results = self.validation_results
        
        print("\n" + "="*50)
        print("🎭 二值掩码验证结果")
        print("="*50)
        
        print(f"📊 总体统计:")
        print(f"   总掩码数: {results['total_masks']}")
        print(f"   成功重建: {results['successful_reconstructions']}")
        print(f"   失败重建: {results['failed_reconstructions']}")
        print(f"   成功率: {results.get('success_rate', 0):.1%}")
        
        if 'avg_iou' in results:
            print(f"\n📐 IoU统计:")
            print(f"   平均IoU: {results['avg_iou']:.3f}")
            print(f"   最小IoU: {results['min_iou']:.3f}")
            print(f"   最大IoU: {results['max_iou']:.3f}")
        
        if 'avg_pixel_accuracy' in results:
            print(f"\n🎯 像素精度统计:")
            print(f"   平均精度: {results['avg_pixel_accuracy']:.3f}")
            print(f"   最小精度: {results['min_pixel_accuracy']:.3f}")
            print(f"   最大精度: {results['max_pixel_accuracy']:.3f}")
        
        # 显示问题掩码
        problem_masks = [r for r in results['detailed_results'] if r.get('iou', 0) < 0.8]
        if problem_masks:
            print(f"\n⚠️ 精度较低的掩码 (IoU < 0.8):")
            for mask in problem_masks[:5]:
                print(f"   - {mask['instance_name']}: IoU={mask['iou']:.3f}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='二值掩码验证工具')
    parser.add_argument('--coco', required=True, help='COCO JSON文件路径')
    parser.add_argument('--images', required=True, help='原始分割图像目录')
    parser.add_argument('--output', default='mask_validation', help='输出目录')
    
    args = parser.parse_args()
    
    # 创建验证器
    validator = BinaryMaskValidator()
    
    # 执行验证
    results = validator.validate_masks(args.coco, args.images, args.output)
    
    # 打印摘要
    validator.print_summary()
    
    # 保存结果
    import json
    with open(os.path.join(args.output, 'mask_validation_results.json'), 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细结果已保存到: {args.output}/mask_validation_results.json")


if __name__ == "__main__":
    main()
