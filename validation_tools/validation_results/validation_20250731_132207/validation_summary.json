{"timestamp": "2025-07-31T13:22:09.797469", "coco_validation": {"status": "error", "message": "Command '['python', 'validate_coco_dataset.py', '--coco', '../output/coco/dataset.json', '--output', 'validation_results/validation_20250731_132207', '--images', '.', '--csv', '../airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv', '--config', 'validation_config.yaml']' returned non-zero exit status 1."}, "mask_validation": {"total_masks": 0, "successful_reconstructions": 0, "failed_reconstructions": 0, "pixel_accuracy_scores": [], "iou_scores": [], "detailed_results": [], "category_masks": {}, "instance_masks": {}}, "performance": {"timestamps": [1753939327.5450659, 1753939328.54974], "memory_usage": [63.4375, 63.453125], "cpu_percent": [0.0, 0.4], "max_memory": 63.453125, "avg_memory": 63.4453125, "memory_increase": 0.046875, "max_cpu": 0.4, "avg_cpu": 0.2}}