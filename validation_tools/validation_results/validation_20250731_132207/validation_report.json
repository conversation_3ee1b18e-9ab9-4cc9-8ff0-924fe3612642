{"errors": [{"timestamp": "2025-07-31T13:22:08.204777", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_23 RGB(191, 127, 127)"}, {"timestamp": "2025-07-31T13:22:08.204783", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var10_lod0_1_InstancedFoliageActor_0 RGB(95, 95, 127)"}, {"timestamp": "2025-07-31T13:22:08.204785", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_25 RGB(191, 127, 191)"}, {"timestamp": "2025-07-31T13:22:08.204787", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var11_lod0_2_InstancedFoliageActor_0 RGB(95, 95, 191)"}, {"timestamp": "2025-07-31T13:22:08.204788", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_22 RGB(191, 127, 255)"}, {"timestamp": "2025-07-31T13:22:08.204790", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var14_lod0_0_InstancedFoliageActor_0 RGB(95, 95, 255)"}, {"timestamp": "2025-07-31T13:22:08.204791", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_35 RGB(191, 255, 95)"}, {"timestamp": "2025-07-31T13:22:08.204793", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_61 RGB(191, 255, 159)"}, {"timestamp": "2025-07-31T13:22:08.204794", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_center RGB(191, 255, 223)"}, {"timestamp": "2025-07-31T13:22:08.204800", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_50 RGB(95, 255, 127)"}, {"timestamp": "2025-07-31T13:22:08.204801", "message": "CSV实例未转换到COCO: water_0_BP_UnderWaterV3_C_1 RGB(95, 255, 191)"}, {"timestamp": "2025-07-31T13:22:08.204803", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_49 RGB(95, 255, 255)"}, {"timestamp": "2025-07-31T13:22:08.204804", "message": "CSV实例未转换到COCO: coral25_0_StaticMeshActor_92 RGB(95, 159, 95)"}, {"timestamp": "2025-07-31T13:22:08.204805", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_BP_PIPCamera_C_1 RGB(223, 223, 255)"}, {"timestamp": "2025-07-31T13:22:08.204811", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_55 RGB(95, 191, 127)"}, {"timestamp": "2025-07-31T13:22:08.204812", "message": "CSV实例未转换到COCO: SM_Coral_V04_0_StaticMeshActor_0 RGB(95, 159, 159)"}, {"timestamp": "2025-07-31T13:22:08.204817", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_right RGB(191, 223, 127)"}, {"timestamp": "2025-07-31T13:22:08.204819", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_56 RGB(95, 191, 191)"}, {"timestamp": "2025-07-31T13:22:08.204820", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_left RGB(95, 159, 223)"}, {"timestamp": "2025-07-31T13:22:08.204822", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_right RGB(191, 223, 191)"}, {"timestamp": "2025-07-31T13:22:08.204823", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_54 RGB(95, 191, 255)"}, {"timestamp": "2025-07-31T13:22:08.204824", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_right RGB(191, 223, 255)"}, {"timestamp": "2025-07-31T13:22:08.204825", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_72 RGB(255, 159, 95)"}, {"timestamp": "2025-07-31T13:22:08.204827", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_94 RGB(255, 159, 159)"}, {"timestamp": "2025-07-31T13:22:08.204828", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_center RGB(255, 159, 223)"}, {"timestamp": "2025-07-31T13:22:08.204830", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_52 RGB(95, 127, 127)"}, {"timestamp": "2025-07-31T13:22:08.204831", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_51 RGB(95, 127, 255)"}, {"timestamp": "2025-07-31T13:22:08.204832", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_33 RGB(127, 127, 95)"}, {"timestamp": "2025-07-31T13:22:08.204835", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var5_9_InstancedFoliageActor_0 RGB(127, 127, 159)"}, {"timestamp": "2025-07-31T13:22:08.204837", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_center RGB(255, 95, 223)"}, {"timestamp": "2025-07-31T13:22:08.204838", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_center RGB(127, 127, 223)"}, {"timestamp": "2025-07-31T13:22:08.204839", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_BP_PIPCamera_C_0 RGB(223, 191, 95)"}, {"timestamp": "2025-07-31T13:22:08.204840", "message": "CSV实例未转换到COCO: EnviroDome_0_HDRIBackdrop_C_1 RGB(95, 255, 95)"}, {"timestamp": "2025-07-31T13:22:08.204842", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_65 RGB(95, 255, 159)"}, {"timestamp": "2025-07-31T13:22:08.204843", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_left RGB(95, 255, 223)"}, {"timestamp": "2025-07-31T13:22:08.204845", "message": "CSV实例未转换到COCO: StaticMeshActor_90 RGB(95, 159, 127)"}, {"timestamp": "2025-07-31T13:22:08.204846", "message": "CSV实例未转换到COCO: StaticMeshActor_91 RGB(95, 159, 191)"}, {"timestamp": "2025-07-31T13:22:08.204847", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_right RGB(255, 223, 127)"}, {"timestamp": "2025-07-31T13:22:08.204853", "message": "CSV实例未转换到COCO: StaticMeshActor_88 RGB(95, 159, 255)"}, {"timestamp": "2025-07-31T13:22:08.204854", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_right RGB(255, 223, 191)"}, {"timestamp": "2025-07-31T13:22:08.204855", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_right RGB(255, 223, 255)"}, {"timestamp": "2025-07-31T13:22:08.204856", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_30 RGB(255, 127, 95)"}, {"timestamp": "2025-07-31T13:22:08.204858", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_gpulidar_gpulidar RGB(255, 255, 79)"}, {"timestamp": "2025-07-31T13:22:08.204859", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var1_5_InstancedFoliageActor_0 RGB(255, 127, 159)"}, {"timestamp": "2025-07-31T13:22:08.204860", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_70 RGB(255, 159, 127)"}, {"timestamp": "2025-07-31T13:22:08.204862", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_71 RGB(255, 159, 191)"}, {"timestamp": "2025-07-31T13:22:08.204863", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_center RGB(255, 127, 223)"}, {"timestamp": "2025-07-31T13:22:08.204864", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_69 RGB(255, 159, 255)"}, {"timestamp": "2025-07-31T13:22:08.204866", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_66 RGB(95, 127, 159)"}, {"timestamp": "2025-07-31T13:22:08.204870", "message": "CSV实例未转换到COCO: airsimvehicle RGB(159, 159, 95)"}, {"timestamp": "2025-07-31T13:22:08.204871", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_17 RGB(127, 191, 127)"}, {"timestamp": "2025-07-31T13:22:08.204873", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_left RGB(95, 127, 223)"}, {"timestamp": "2025-07-31T13:22:08.204874", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_center RGB(159, 159, 159)"}, {"timestamp": "2025-07-31T13:22:08.204875", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_19 RGB(127, 191, 191)"}, {"timestamp": "2025-07-31T13:22:08.204877", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_left RGB(159, 159, 223)"}, {"timestamp": "2025-07-31T13:22:08.204878", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_16 RGB(127, 191, 255)"}, {"timestamp": "2025-07-31T13:22:08.204879", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_47 RGB(127, 95, 95)"}, {"timestamp": "2025-07-31T13:22:08.204881", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_BP_PIPCamera_C_1 RGB(223, 159, 95)"}, {"timestamp": "2025-07-31T13:22:08.204882", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_8 RGB(127, 127, 127)"}, {"timestamp": "2025-07-31T13:22:08.204883", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_60 RGB(127, 95, 159)"}, {"timestamp": "2025-07-31T13:22:08.204887", "message": "CSV实例未转换到COCO: coral25_0_StaticMeshActor_41 RGB(255, 95, 191)"}, {"timestamp": "2025-07-31T13:22:08.204889", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_12 RGB(127, 127, 191)"}, {"timestamp": "2025-07-31T13:22:08.204890", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_center RGB(127, 95, 223)"}, {"timestamp": "2025-07-31T13:22:08.204891", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_7 RGB(127, 127, 255)"}, {"timestamp": "2025-07-31T13:22:08.204893", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_BP_PIPCamera_C_1 RGB(223, 159, 159)"}, {"timestamp": "2025-07-31T13:22:08.204894", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_BP_PIPCamera_C_1 RGB(223, 159, 223)"}, {"timestamp": "2025-07-31T13:22:08.204895", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_gpulidar_gpulidar RGB(255, 127, 79)"}, {"timestamp": "2025-07-31T13:22:08.204897", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_2 RGB(255, 255, 127)"}, {"timestamp": "2025-07-31T13:22:08.204898", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_9 RGB(255, 255, 191)"}, {"timestamp": "2025-07-31T13:22:08.204899", "message": "CSV实例未转换到COCO: SM_Template_Map_Floor_0_Floor RGB(255, 255, 255)"}, {"timestamp": "2025-07-31T13:22:08.205395", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_driver RGB(95, 223, 95)"}, {"timestamp": "2025-07-31T13:22:08.205397", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_driver RGB(95, 223, 159)"}, {"timestamp": "2025-07-31T13:22:08.205398", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_back_center RGB(95, 223, 223)"}, {"timestamp": "2025-07-31T13:22:08.205399", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_right RGB(255, 223, 95)"}, {"timestamp": "2025-07-31T13:22:08.205401", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_BP_PIPCamera_C_0 RGB(223, 95, 191)"}, {"timestamp": "2025-07-31T13:22:08.205402", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_right RGB(255, 223, 159)"}, {"timestamp": "2025-07-31T13:22:08.205403", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_driver RGB(255, 223, 223)"}, {"timestamp": "2025-07-31T13:22:08.205405", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_4 RGB(255, 127, 127)"}, {"timestamp": "2025-07-31T13:22:08.205407", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_10 RGB(255, 127, 191)"}, {"timestamp": "2025-07-31T13:22:08.205408", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_3 RGB(255, 127, 255)"}, {"timestamp": "2025-07-31T13:22:08.205409", "message": "CSV实例未转换到COCO: shuangdaigongziaohei_0_StaticMeshActor_112 RGB(159, 127, 95)"}, {"timestamp": "2025-07-31T13:22:08.205410", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_34 RGB(127, 191, 95)"}, {"timestamp": "2025-07-31T13:22:08.205412", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_ExternalCamera RGB(159, 127, 159)"}, {"timestamp": "2025-07-31T13:22:08.205413", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_59 RGB(127, 191, 159)"}, {"timestamp": "2025-07-31T13:22:08.205414", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_ExternalCamera RGB(159, 159, 127)"}, {"timestamp": "2025-07-31T13:22:08.205416", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_ExternalCamera RGB(159, 159, 191)"}, {"timestamp": "2025-07-31T13:22:08.205417", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_center RGB(127, 191, 223)"}, {"timestamp": "2025-07-31T13:22:08.205418", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_left RGB(159, 127, 223)"}, {"timestamp": "2025-07-31T13:22:08.205420", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_ExternalCamera RGB(159, 159, 255)"}, {"timestamp": "2025-07-31T13:22:08.205421", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_46 RGB(255, 95, 95)"}, {"timestamp": "2025-07-31T13:22:08.205422", "message": "CSV实例未转换到COCO: SM_CineCam_1_CineCameraActor_0 RGB(127, 95, 127)"}, {"timestamp": "2025-07-31T13:22:08.205423", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_back_center RGB(223, 127, 95)"}, {"timestamp": "2025-07-31T13:22:08.205425", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var3_7_InstancedFoliageActor_0 RGB(255, 95, 159)"}, {"timestamp": "2025-07-31T13:22:08.205426", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_42 RGB(127, 95, 191)"}, {"timestamp": "2025-07-31T13:22:08.205428", "message": "CSV实例未转换到COCO: SM_CineCam_0_BP_PIPCamera_C_0 RGB(223, 127, 159)"}, {"timestamp": "2025-07-31T13:22:08.205429", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_BP_PIPCamera_C_0 RGB(223, 159, 127)"}, {"timestamp": "2025-07-31T13:22:08.205430", "message": "CSV实例未转换到COCO: SM_CineCam_0_CineCameraActor_0 RGB(127, 95, 255)"}, {"timestamp": "2025-07-31T13:22:08.205432", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_BP_PIPCamera_C_0 RGB(223, 159, 255)"}, {"timestamp": "2025-07-31T13:22:08.205433", "message": "CSV实例未转换到COCO: SM_CineCam_0_BP_PIPCamera_C_1 RGB(223, 159, 191)"}, {"timestamp": "2025-07-31T13:22:08.205434", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_BP_PIPCamera_C_1 RGB(223, 127, 223)"}, {"timestamp": "2025-07-31T13:22:08.205436", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var13_lod0_4_InstancedFoliageActor_0 RGB(255, 255, 159)"}, {"timestamp": "2025-07-31T13:22:08.205437", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_center RGB(255, 255, 223)"}, {"timestamp": "2025-07-31T13:22:08.205440", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_BP_PIPCamera_C_0 RGB(223, 95, 127)"}, {"timestamp": "2025-07-31T13:22:08.205441", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_driver RGB(95, 223, 127)"}, {"timestamp": "2025-07-31T13:22:08.205442", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_48 RGB(191, 95, 95)"}, {"timestamp": "2025-07-31T13:22:08.205444", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_driver RGB(95, 223, 191)"}, {"timestamp": "2025-07-31T13:22:08.205445", "message": "CSV实例未转换到COCO: tongshiguiyu_0_StaticMeshActor_93 RGB(159, 255, 127)"}, {"timestamp": "2025-07-31T13:22:08.205446", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_64 RGB(191, 95, 159)"}, {"timestamp": "2025-07-31T13:22:08.205447", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_driver RGB(95, 223, 255)"}, {"timestamp": "2025-07-31T13:22:08.205449", "message": "CSV实例未转换到COCO: shitoujinyu_0_StaticMeshActor_98 RGB(159, 255, 191)"}, {"timestamp": "2025-07-31T13:22:08.205450", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_left RGB(191, 95, 223)"}, {"timestamp": "2025-07-31T13:22:08.205452", "message": "CSV实例未转换到COCO: Cone_6_BP_PIPCamera_C_0 RGB(223, 95, 255)"}, {"timestamp": "2025-07-31T13:22:08.205453", "message": "CSV实例未转换到COCO: base_basic_shaded_0_StaticMeshActor_15 RGB(159, 255, 255)"}, {"timestamp": "2025-07-31T13:22:08.205454", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_31 RGB(255, 191, 95)"}, {"timestamp": "2025-07-31T13:22:08.205455", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var2_6_InstancedFoliageActor_0 RGB(255, 191, 159)"}, {"timestamp": "2025-07-31T13:22:08.205457", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_center RGB(255, 191, 223)"}, {"timestamp": "2025-07-31T13:22:08.205458", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_driver RGB(159, 223, 95)"}, {"timestamp": "2025-07-31T13:22:08.205459", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_6 RGB(127, 255, 127)"}, {"timestamp": "2025-07-31T13:22:08.205461", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_driver RGB(159, 223, 159)"}, {"timestamp": "2025-07-31T13:22:08.205462", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_11 RGB(127, 255, 191)"}, {"timestamp": "2025-07-31T13:22:08.205463", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_back_center RGB(159, 223, 223)"}, {"timestamp": "2025-07-31T13:22:08.205464", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_5 RGB(127, 255, 255)"}, {"timestamp": "2025-07-31T13:22:08.205466", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_76 RGB(127, 159, 95)"}, {"timestamp": "2025-07-31T13:22:08.205467", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_BP_PIPCamera_C_1 RGB(223, 223, 95)"}, {"timestamp": "2025-07-31T13:22:08.205468", "message": "CSV实例未转换到COCO: chixuehonglongye_0_StaticMeshActor_108 RGB(159, 127, 127)"}, {"timestamp": "2025-07-31T13:22:08.205470", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_95 RGB(127, 159, 159)"}, {"timestamp": "2025-07-31T13:22:08.205471", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_BP_PIPCamera_C_1 RGB(223, 223, 159)"}, {"timestamp": "2025-07-31T13:22:08.205472", "message": "CSV实例未转换到COCO: yinnihuyu_0_StaticMeshActor_110 RGB(159, 127, 191)"}, {"timestamp": "2025-07-31T13:22:08.205473", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_center RGB(127, 159, 223)"}, {"timestamp": "2025-07-31T13:22:08.205475", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_left RGB(159, 95, 223)"}, {"timestamp": "2025-07-31T13:22:08.205476", "message": "CSV实例未转换到COCO: niluokoufeiji_0_StaticMeshActor_104 RGB(159, 127, 255)"}, {"timestamp": "2025-07-31T13:22:08.205477", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_driver RGB(159, 223, 191)"}, {"timestamp": "2025-07-31T13:22:08.205479", "message": "CSV实例未转换到COCO: MatineeCam_SM_0_airsimvehicle_gpulidar_gpulidar RGB(223, 223, 223)"}, {"timestamp": "2025-07-31T13:22:08.205480", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_back_center RGB(223, 127, 127)"}, {"timestamp": "2025-07-31T13:22:08.205481", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_back_center RGB(223, 127, 191)"}, {"timestamp": "2025-07-31T13:22:08.205483", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_back_center RGB(223, 127, 255)"}, {"timestamp": "2025-07-31T13:22:08.205484", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_BP_PIPCamera_C_0 RGB(223, 95, 95)"}, {"timestamp": "2025-07-31T13:22:08.205485", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_BP_PIPCamera_C_0 RGB(223, 95, 159)"}, {"timestamp": "2025-07-31T13:22:08.205486", "message": "CSV实例未转换到COCO: hongdidiao_0_StaticMeshActor_100 RGB(159, 255, 95)"}, {"timestamp": "2025-07-31T13:22:08.205488", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_44 RGB(191, 95, 127)"}, {"timestamp": "2025-07-31T13:22:08.205489", "message": "CSV实例未转换到COCO: Cone_6_BP_PIPCamera_C_1 RGB(223, 95, 223)"}, {"timestamp": "2025-07-31T13:22:08.205494", "message": "CSV实例未转换到COCO: Cone_6_ExternalCamera RGB(159, 255, 159)"}, {"timestamp": "2025-07-31T13:22:08.205495", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_45 RGB(191, 95, 191)"}, {"timestamp": "2025-07-31T13:22:08.205497", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_left RGB(159, 255, 223)"}, {"timestamp": "2025-07-31T13:22:08.205498", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_43 RGB(191, 95, 255)"}, {"timestamp": "2025-07-31T13:22:08.205500", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_14 RGB(255, 191, 127)"}, {"timestamp": "2025-07-31T13:22:08.205502", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_18 RGB(255, 191, 191)"}, {"timestamp": "2025-07-31T13:22:08.205503", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_13 RGB(255, 191, 255)"}, {"timestamp": "2025-07-31T13:22:08.205504", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_ExternalCamera RGB(159, 191, 95)"}, {"timestamp": "2025-07-31T13:22:08.205505", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_32 RGB(127, 255, 95)"}, {"timestamp": "2025-07-31T13:22:08.205507", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_right RGB(127, 223, 127)"}, {"timestamp": "2025-07-31T13:22:08.205508", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_ExternalCamera RGB(159, 191, 159)"}, {"timestamp": "2025-07-31T13:22:08.205509", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var4_8_InstancedFoliageActor_0 RGB(127, 255, 159)"}, {"timestamp": "2025-07-31T13:22:08.205511", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_right RGB(127, 223, 191)"}, {"timestamp": "2025-07-31T13:22:08.205512", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_left RGB(159, 191, 223)"}, {"timestamp": "2025-07-31T13:22:08.205513", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_center RGB(127, 255, 223)"}, {"timestamp": "2025-07-31T13:22:08.205514", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_right RGB(127, 223, 255)"}, {"timestamp": "2025-07-31T13:22:08.205516", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_driver RGB(159, 223, 255)"}, {"timestamp": "2025-07-31T13:22:08.205517", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_ExternalCamera RGB(159, 95, 127)"}, {"timestamp": "2025-07-31T13:22:08.205518", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_74 RGB(127, 159, 127)"}, {"timestamp": "2025-07-31T13:22:08.205519", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_driver RGB(159, 223, 127)"}, {"timestamp": "2025-07-31T13:22:08.205521", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_ExternalCamera RGB(159, 95, 191)"}, {"timestamp": "2025-07-31T13:22:08.205522", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_75 RGB(127, 159, 191)"}, {"timestamp": "2025-07-31T13:22:08.205523", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_ExternalCamera RGB(159, 95, 255)"}, {"timestamp": "2025-07-31T13:22:08.205525", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_73 RGB(127, 159, 255)"}, {"timestamp": "2025-07-31T13:22:08.205526", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_BP_PIPCamera_C_0 RGB(223, 191, 159)"}, {"timestamp": "2025-07-31T13:22:08.205527", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_BP_PIPCamera_C_1 RGB(223, 191, 223)"}, {"timestamp": "2025-07-31T13:22:08.205528", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_26 RGB(191, 191, 255)"}, {"timestamp": "2025-07-31T13:22:08.205530", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_BP_PIPCamera_C_1 RGB(223, 223, 191)"}, {"timestamp": "2025-07-31T13:22:08.205531", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_back_center RGB(191, 223, 223)"}, {"timestamp": "2025-07-31T13:22:08.205532", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_BP_PIPCamera_C_1 RGB(223, 223, 127)"}, {"timestamp": "2025-07-31T13:22:08.205534", "message": "CSV实例未转换到COCO: coral18_0_StaticMeshActor_81 RGB(191, 159, 95)"}, {"timestamp": "2025-07-31T13:22:08.205535", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_96 RGB(191, 159, 159)"}, {"timestamp": "2025-07-31T13:22:08.205536", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_left RGB(191, 159, 223)"}, {"timestamp": "2025-07-31T13:22:08.205543", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_driver RGB(191, 223, 159)"}, {"timestamp": "2025-07-31T13:22:08.205544", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_right RGB(127, 223, 95)"}, {"timestamp": "2025-07-31T13:22:08.205545", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_back_center RGB(223, 255, 127)"}, {"timestamp": "2025-07-31T13:22:08.205546", "message": "CSV实例未转换到COCO: shayu_0_StaticMeshActor_116 RGB(159, 191, 127)"}, {"timestamp": "2025-07-31T13:22:08.205548", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_right RGB(127, 223, 159)"}, {"timestamp": "2025-07-31T13:22:08.205549", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_back_center RGB(223, 255, 191)"}, {"timestamp": "2025-07-31T13:22:08.205550", "message": "CSV实例未转换到COCO: SM_CineCam_0_ExternalCamera RGB(159, 191, 191)"}, {"timestamp": "2025-07-31T13:22:08.205551", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_back_center RGB(127, 223, 223)"}, {"timestamp": "2025-07-31T13:22:08.205553", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_back_center RGB(223, 255, 255)"}, {"timestamp": "2025-07-31T13:22:08.205554", "message": "CSV实例未转换到COCO: s<PERSON>wangdouyu_0_StaticMeshActor_114 RGB(159, 191, 255)"}, {"timestamp": "2025-07-31T13:22:08.205555", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_ExternalCamera RGB(159, 95, 95)"}, {"timestamp": "2025-07-31T13:22:08.205557", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_BP_PIPCamera_C_0 RGB(223, 191, 127)"}, {"timestamp": "2025-07-31T13:22:08.205559", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_ExternalCamera RGB(159, 95, 159)"}, {"timestamp": "2025-07-31T13:22:08.205560", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_37 RGB(191, 191, 95)"}, {"timestamp": "2025-07-31T13:22:08.205561", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_BP_PIPCamera_C_0 RGB(223, 191, 191)"}, {"timestamp": "2025-07-31T13:22:08.205563", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_63 RGB(191, 191, 159)"}, {"timestamp": "2025-07-31T13:22:08.205564", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_BP_PIPCamera_C_0 RGB(223, 191, 255)"}, {"timestamp": "2025-07-31T13:22:08.205565", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_left RGB(191, 191, 223)"}, {"timestamp": "2025-07-31T13:22:08.205566", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_36 RGB(191, 127, 95)"}, {"timestamp": "2025-07-31T13:22:08.205568", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var12_lod0_3_InstancedFoliageActor_0 RGB(95, 95, 95)"}, {"timestamp": "2025-07-31T13:22:08.205569", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_62 RGB(191, 127, 159)"}, {"timestamp": "2025-07-31T13:22:08.205570", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_68 RGB(95, 95, 159)"}, {"timestamp": "2025-07-31T13:22:08.205572", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_78 RGB(191, 159, 127)"}, {"timestamp": "2025-07-31T13:22:08.205573", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_79 RGB(191, 159, 191)"}, {"timestamp": "2025-07-31T13:22:08.205574", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_center RGB(191, 127, 223)"}, {"timestamp": "2025-07-31T13:22:08.205575", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_left RGB(95, 95, 223)"}, {"timestamp": "2025-07-31T13:22:08.205577", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_77 RGB(191, 159, 255)"}, {"timestamp": "2025-07-31T13:22:08.205578", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_back_center RGB(223, 255, 95)"}, {"timestamp": "2025-07-31T13:22:08.205579", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_back_center RGB(223, 255, 159)"}, {"timestamp": "2025-07-31T13:22:08.205581", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_21 RGB(191, 255, 127)"}, {"timestamp": "2025-07-31T13:22:08.205582", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_BP_PIPCamera_C_1 RGB(223, 255, 223)"}, {"timestamp": "2025-07-31T13:22:08.205583", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_24 RGB(191, 255, 191)"}, {"timestamp": "2025-07-31T13:22:08.205584", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_20 RGB(191, 255, 255)"}, {"timestamp": "2025-07-31T13:22:08.205586", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_driver RGB(191, 223, 95)"}, {"timestamp": "2025-07-31T13:22:08.205587", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_67 RGB(95, 191, 159)"}, {"timestamp": "2025-07-31T13:22:08.205588", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_left RGB(95, 191, 223)"}], "warnings": [{"timestamp": "2025-07-31T13:22:08.205636", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205642", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205646", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205649", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205652", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205655", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205657", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205660", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205662", "message": "图像文件不存在: testmask.jpg"}, {"timestamp": "2025-07-31T13:22:08.205665", "message": "图像文件不存在: testmask.jpg"}], "info": [{"timestamp": "2025-07-31T13:22:08.196236", "message": "成功加载COCO文件: ../output/coco/dataset.json"}, {"timestamp": "2025-07-31T13:22:08.196247", "message": "开始COCO格式规范验证..."}, {"timestamp": "2025-07-31T13:22:08.196313", "message": "开始数据关联性验证..."}, {"timestamp": "2025-07-31T13:22:08.196331", "message": "开始实例分割特异性验证..."}, {"timestamp": "2025-07-31T13:22:08.205618", "message": "开始几何精度验证..."}, {"timestamp": "2025-07-31T13:22:08.206499", "message": "生成可视化图像..."}, {"timestamp": "2025-07-31T13:22:08.452108", "message": "生成验证报告..."}], "statistics": {"csv_instances": 218, "coco_instances": 8, "matched_instances": 8, "missing_instances": 210, "extra_instances": 0, "geometry": {"total_annotations": 10, "bbox_accuracy_issues": 0, "segmentation_issues": 0, "area_mismatches": 0}, "basic": {"total_images": 1, "total_annotations": 10, "total_categories": 9, "avg_annotations_per_image": 10.0}, "categories": {"1": 10}, "areas": {"min_area": 401.5, "max_area": 155059.0, "mean_area": 32501.85, "median_area": 3396.25}}, "validation_time": null}