{"errors": [{"timestamp": "2025-07-31T13:23:17.991392", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_23 RGB(191, 127, 127)"}, {"timestamp": "2025-07-31T13:23:17.991396", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var10_lod0_1_InstancedFoliageActor_0 RGB(95, 95, 127)"}, {"timestamp": "2025-07-31T13:23:17.991398", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_25 RGB(191, 127, 191)"}, {"timestamp": "2025-07-31T13:23:17.991400", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var11_lod0_2_InstancedFoliageActor_0 RGB(95, 95, 191)"}, {"timestamp": "2025-07-31T13:23:17.991401", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_22 RGB(191, 127, 255)"}, {"timestamp": "2025-07-31T13:23:17.991403", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var14_lod0_0_InstancedFoliageActor_0 RGB(95, 95, 255)"}, {"timestamp": "2025-07-31T13:23:17.991404", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_35 RGB(191, 255, 95)"}, {"timestamp": "2025-07-31T13:23:17.991406", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_61 RGB(191, 255, 159)"}, {"timestamp": "2025-07-31T13:23:17.991407", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_center RGB(191, 255, 223)"}, {"timestamp": "2025-07-31T13:23:17.991408", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_50 RGB(95, 255, 127)"}, {"timestamp": "2025-07-31T13:23:17.991409", "message": "CSV实例未转换到COCO: water_0_BP_UnderWaterV3_C_1 RGB(95, 255, 191)"}, {"timestamp": "2025-07-31T13:23:17.991411", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_49 RGB(95, 255, 255)"}, {"timestamp": "2025-07-31T13:23:17.991412", "message": "CSV实例未转换到COCO: coral25_0_StaticMeshActor_92 RGB(95, 159, 95)"}, {"timestamp": "2025-07-31T13:23:17.991413", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_BP_PIPCamera_C_1 RGB(223, 223, 255)"}, {"timestamp": "2025-07-31T13:23:17.991416", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_55 RGB(95, 191, 127)"}, {"timestamp": "2025-07-31T13:23:17.991418", "message": "CSV实例未转换到COCO: SM_Coral_V04_0_StaticMeshActor_0 RGB(95, 159, 159)"}, {"timestamp": "2025-07-31T13:23:17.991419", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_right RGB(191, 223, 127)"}, {"timestamp": "2025-07-31T13:23:17.991420", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_56 RGB(95, 191, 191)"}, {"timestamp": "2025-07-31T13:23:17.991422", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_left RGB(95, 159, 223)"}, {"timestamp": "2025-07-31T13:23:17.991423", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_right RGB(191, 223, 191)"}, {"timestamp": "2025-07-31T13:23:17.991424", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_54 RGB(95, 191, 255)"}, {"timestamp": "2025-07-31T13:23:17.991426", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_right RGB(191, 223, 255)"}, {"timestamp": "2025-07-31T13:23:17.991427", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_72 RGB(255, 159, 95)"}, {"timestamp": "2025-07-31T13:23:17.991428", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_94 RGB(255, 159, 159)"}, {"timestamp": "2025-07-31T13:23:17.991430", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_center RGB(255, 159, 223)"}, {"timestamp": "2025-07-31T13:23:17.991431", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_52 RGB(95, 127, 127)"}, {"timestamp": "2025-07-31T13:23:17.991432", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_51 RGB(95, 127, 255)"}, {"timestamp": "2025-07-31T13:23:17.991434", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_33 RGB(127, 127, 95)"}, {"timestamp": "2025-07-31T13:23:17.991435", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var5_9_InstancedFoliageActor_0 RGB(127, 127, 159)"}, {"timestamp": "2025-07-31T13:23:17.991436", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_center RGB(255, 95, 223)"}, {"timestamp": "2025-07-31T13:23:17.991438", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_center RGB(127, 127, 223)"}, {"timestamp": "2025-07-31T13:23:17.991439", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_BP_PIPCamera_C_0 RGB(223, 191, 95)"}, {"timestamp": "2025-07-31T13:23:17.991440", "message": "CSV实例未转换到COCO: EnviroDome_0_HDRIBackdrop_C_1 RGB(95, 255, 95)"}, {"timestamp": "2025-07-31T13:23:17.991442", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_65 RGB(95, 255, 159)"}, {"timestamp": "2025-07-31T13:23:17.991443", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_left RGB(95, 255, 223)"}, {"timestamp": "2025-07-31T13:23:17.991444", "message": "CSV实例未转换到COCO: StaticMeshActor_90 RGB(95, 159, 127)"}, {"timestamp": "2025-07-31T13:23:17.991445", "message": "CSV实例未转换到COCO: StaticMeshActor_91 RGB(95, 159, 191)"}, {"timestamp": "2025-07-31T13:23:17.991447", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_right RGB(255, 223, 127)"}, {"timestamp": "2025-07-31T13:23:17.991448", "message": "CSV实例未转换到COCO: StaticMeshActor_88 RGB(95, 159, 255)"}, {"timestamp": "2025-07-31T13:23:17.991449", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_right RGB(255, 223, 191)"}, {"timestamp": "2025-07-31T13:23:17.991451", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_right RGB(255, 223, 255)"}, {"timestamp": "2025-07-31T13:23:17.991452", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_30 RGB(255, 127, 95)"}, {"timestamp": "2025-07-31T13:23:17.991453", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_gpulidar_gpulidar RGB(255, 255, 79)"}, {"timestamp": "2025-07-31T13:23:17.991455", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var1_5_InstancedFoliageActor_0 RGB(255, 127, 159)"}, {"timestamp": "2025-07-31T13:23:17.991456", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_70 RGB(255, 159, 127)"}, {"timestamp": "2025-07-31T13:23:17.991457", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_71 RGB(255, 159, 191)"}, {"timestamp": "2025-07-31T13:23:17.991458", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_center RGB(255, 127, 223)"}, {"timestamp": "2025-07-31T13:23:17.991460", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_69 RGB(255, 159, 255)"}, {"timestamp": "2025-07-31T13:23:17.991461", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_66 RGB(95, 127, 159)"}, {"timestamp": "2025-07-31T13:23:17.991462", "message": "CSV实例未转换到COCO: airsimvehicle RGB(159, 159, 95)"}, {"timestamp": "2025-07-31T13:23:17.991464", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_17 RGB(127, 191, 127)"}, {"timestamp": "2025-07-31T13:23:17.991465", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_left RGB(95, 127, 223)"}, {"timestamp": "2025-07-31T13:23:17.991466", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_center RGB(159, 159, 159)"}, {"timestamp": "2025-07-31T13:23:17.991467", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_19 RGB(127, 191, 191)"}, {"timestamp": "2025-07-31T13:23:17.991469", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_left RGB(159, 159, 223)"}, {"timestamp": "2025-07-31T13:23:17.991470", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_16 RGB(127, 191, 255)"}, {"timestamp": "2025-07-31T13:23:17.991471", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_47 RGB(127, 95, 95)"}, {"timestamp": "2025-07-31T13:23:17.991473", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_BP_PIPCamera_C_1 RGB(223, 159, 95)"}, {"timestamp": "2025-07-31T13:23:17.991474", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_8 RGB(127, 127, 127)"}, {"timestamp": "2025-07-31T13:23:17.991475", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_60 RGB(127, 95, 159)"}, {"timestamp": "2025-07-31T13:23:17.991476", "message": "CSV实例未转换到COCO: coral25_0_StaticMeshActor_41 RGB(255, 95, 191)"}, {"timestamp": "2025-07-31T13:23:17.991478", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_12 RGB(127, 127, 191)"}, {"timestamp": "2025-07-31T13:23:17.991479", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_center RGB(127, 95, 223)"}, {"timestamp": "2025-07-31T13:23:17.991480", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_7 RGB(127, 127, 255)"}, {"timestamp": "2025-07-31T13:23:17.991481", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_BP_PIPCamera_C_1 RGB(223, 159, 159)"}, {"timestamp": "2025-07-31T13:23:17.991483", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_BP_PIPCamera_C_1 RGB(223, 159, 223)"}, {"timestamp": "2025-07-31T13:23:17.991484", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_gpulidar_gpulidar RGB(255, 127, 79)"}, {"timestamp": "2025-07-31T13:23:17.991485", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_2 RGB(255, 255, 127)"}, {"timestamp": "2025-07-31T13:23:17.991487", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_9 RGB(255, 255, 191)"}, {"timestamp": "2025-07-31T13:23:17.991488", "message": "CSV实例未转换到COCO: SM_Template_Map_Floor_0_Floor RGB(255, 255, 255)"}, {"timestamp": "2025-07-31T13:23:17.991922", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_driver RGB(95, 223, 95)"}, {"timestamp": "2025-07-31T13:23:17.991926", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_driver RGB(95, 223, 159)"}, {"timestamp": "2025-07-31T13:23:17.991928", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_back_center RGB(95, 223, 223)"}, {"timestamp": "2025-07-31T13:23:17.991929", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_right RGB(255, 223, 95)"}, {"timestamp": "2025-07-31T13:23:17.991930", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_BP_PIPCamera_C_0 RGB(223, 95, 191)"}, {"timestamp": "2025-07-31T13:23:17.991932", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_right RGB(255, 223, 159)"}, {"timestamp": "2025-07-31T13:23:17.991933", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_driver RGB(255, 223, 223)"}, {"timestamp": "2025-07-31T13:23:17.991935", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_4 RGB(255, 127, 127)"}, {"timestamp": "2025-07-31T13:23:17.991936", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_10 RGB(255, 127, 191)"}, {"timestamp": "2025-07-31T13:23:17.991937", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_3 RGB(255, 127, 255)"}, {"timestamp": "2025-07-31T13:23:17.991939", "message": "CSV实例未转换到COCO: shuangdaigongziaohei_0_StaticMeshActor_112 RGB(159, 127, 95)"}, {"timestamp": "2025-07-31T13:23:17.991940", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_34 RGB(127, 191, 95)"}, {"timestamp": "2025-07-31T13:23:17.991941", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_ExternalCamera RGB(159, 127, 159)"}, {"timestamp": "2025-07-31T13:23:17.991942", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_59 RGB(127, 191, 159)"}, {"timestamp": "2025-07-31T13:23:17.991944", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_ExternalCamera RGB(159, 159, 127)"}, {"timestamp": "2025-07-31T13:23:17.991945", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_ExternalCamera RGB(159, 159, 191)"}, {"timestamp": "2025-07-31T13:23:17.991946", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_center RGB(127, 191, 223)"}, {"timestamp": "2025-07-31T13:23:17.991947", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_left RGB(159, 127, 223)"}, {"timestamp": "2025-07-31T13:23:17.991949", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_ExternalCamera RGB(159, 159, 255)"}, {"timestamp": "2025-07-31T13:23:17.991950", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_46 RGB(255, 95, 95)"}, {"timestamp": "2025-07-31T13:23:17.991951", "message": "CSV实例未转换到COCO: SM_CineCam_1_CineCameraActor_0 RGB(127, 95, 127)"}, {"timestamp": "2025-07-31T13:23:17.991952", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_back_center RGB(223, 127, 95)"}, {"timestamp": "2025-07-31T13:23:17.991954", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var3_7_InstancedFoliageActor_0 RGB(255, 95, 159)"}, {"timestamp": "2025-07-31T13:23:17.991957", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_42 RGB(127, 95, 191)"}, {"timestamp": "2025-07-31T13:23:17.991958", "message": "CSV实例未转换到COCO: SM_CineCam_0_BP_PIPCamera_C_0 RGB(223, 127, 159)"}, {"timestamp": "2025-07-31T13:23:17.991960", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_BP_PIPCamera_C_0 RGB(223, 159, 127)"}, {"timestamp": "2025-07-31T13:23:17.991961", "message": "CSV实例未转换到COCO: SM_CineCam_0_CineCameraActor_0 RGB(127, 95, 255)"}, {"timestamp": "2025-07-31T13:23:17.991962", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_BP_PIPCamera_C_0 RGB(223, 159, 255)"}, {"timestamp": "2025-07-31T13:23:17.991963", "message": "CSV实例未转换到COCO: SM_CineCam_0_BP_PIPCamera_C_1 RGB(223, 159, 191)"}, {"timestamp": "2025-07-31T13:23:17.991965", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_BP_PIPCamera_C_1 RGB(223, 127, 223)"}, {"timestamp": "2025-07-31T13:23:17.991967", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var13_lod0_4_InstancedFoliageActor_0 RGB(255, 255, 159)"}, {"timestamp": "2025-07-31T13:23:17.991969", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_center RGB(255, 255, 223)"}, {"timestamp": "2025-07-31T13:23:17.991970", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_BP_PIPCamera_C_0 RGB(223, 95, 127)"}, {"timestamp": "2025-07-31T13:23:17.991971", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_driver RGB(95, 223, 127)"}, {"timestamp": "2025-07-31T13:23:17.991973", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_48 RGB(191, 95, 95)"}, {"timestamp": "2025-07-31T13:23:17.991974", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_driver RGB(95, 223, 191)"}, {"timestamp": "2025-07-31T13:23:17.991975", "message": "CSV实例未转换到COCO: tongshiguiyu_0_StaticMeshActor_93 RGB(159, 255, 127)"}, {"timestamp": "2025-07-31T13:23:17.991976", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_64 RGB(191, 95, 159)"}, {"timestamp": "2025-07-31T13:23:17.991978", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_driver RGB(95, 223, 255)"}, {"timestamp": "2025-07-31T13:23:17.991979", "message": "CSV实例未转换到COCO: shitoujinyu_0_StaticMeshActor_98 RGB(159, 255, 191)"}, {"timestamp": "2025-07-31T13:23:17.991980", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_left RGB(191, 95, 223)"}, {"timestamp": "2025-07-31T13:23:17.991982", "message": "CSV实例未转换到COCO: Cone_6_BP_PIPCamera_C_0 RGB(223, 95, 255)"}, {"timestamp": "2025-07-31T13:23:17.991983", "message": "CSV实例未转换到COCO: base_basic_shaded_0_StaticMeshActor_15 RGB(159, 255, 255)"}, {"timestamp": "2025-07-31T13:23:17.991984", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_31 RGB(255, 191, 95)"}, {"timestamp": "2025-07-31T13:23:17.991986", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var2_6_InstancedFoliageActor_0 RGB(255, 191, 159)"}, {"timestamp": "2025-07-31T13:23:17.991987", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_center RGB(255, 191, 223)"}, {"timestamp": "2025-07-31T13:23:17.991988", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_driver RGB(159, 223, 95)"}, {"timestamp": "2025-07-31T13:23:17.991989", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_6 RGB(127, 255, 127)"}, {"timestamp": "2025-07-31T13:23:17.991991", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_driver RGB(159, 223, 159)"}, {"timestamp": "2025-07-31T13:23:17.991992", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_11 RGB(127, 255, 191)"}, {"timestamp": "2025-07-31T13:23:17.991993", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_back_center RGB(159, 223, 223)"}, {"timestamp": "2025-07-31T13:23:17.991995", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_5 RGB(127, 255, 255)"}, {"timestamp": "2025-07-31T13:23:17.991996", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_76 RGB(127, 159, 95)"}, {"timestamp": "2025-07-31T13:23:17.991997", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_BP_PIPCamera_C_1 RGB(223, 223, 95)"}, {"timestamp": "2025-07-31T13:23:17.991998", "message": "CSV实例未转换到COCO: chixuehonglongye_0_StaticMeshActor_108 RGB(159, 127, 127)"}, {"timestamp": "2025-07-31T13:23:17.992000", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_95 RGB(127, 159, 159)"}, {"timestamp": "2025-07-31T13:23:17.992001", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_BP_PIPCamera_C_1 RGB(223, 223, 159)"}, {"timestamp": "2025-07-31T13:23:17.992002", "message": "CSV实例未转换到COCO: yinnihuyu_0_StaticMeshActor_110 RGB(159, 127, 191)"}, {"timestamp": "2025-07-31T13:23:17.992003", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_center RGB(127, 159, 223)"}, {"timestamp": "2025-07-31T13:23:17.992005", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_left RGB(159, 95, 223)"}, {"timestamp": "2025-07-31T13:23:17.992006", "message": "CSV实例未转换到COCO: niluokoufeiji_0_StaticMeshActor_104 RGB(159, 127, 255)"}, {"timestamp": "2025-07-31T13:23:17.992007", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_driver RGB(159, 223, 191)"}, {"timestamp": "2025-07-31T13:23:17.992009", "message": "CSV实例未转换到COCO: MatineeCam_SM_0_airsimvehicle_gpulidar_gpulidar RGB(223, 223, 223)"}, {"timestamp": "2025-07-31T13:23:17.992010", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_back_center RGB(223, 127, 127)"}, {"timestamp": "2025-07-31T13:23:17.992011", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_back_center RGB(223, 127, 191)"}, {"timestamp": "2025-07-31T13:23:17.992013", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_back_center RGB(223, 127, 255)"}, {"timestamp": "2025-07-31T13:23:17.992014", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_BP_PIPCamera_C_0 RGB(223, 95, 95)"}, {"timestamp": "2025-07-31T13:23:17.992015", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_BP_PIPCamera_C_0 RGB(223, 95, 159)"}, {"timestamp": "2025-07-31T13:23:17.992016", "message": "CSV实例未转换到COCO: hongdidiao_0_StaticMeshActor_100 RGB(159, 255, 95)"}, {"timestamp": "2025-07-31T13:23:17.992018", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_44 RGB(191, 95, 127)"}, {"timestamp": "2025-07-31T13:23:17.992019", "message": "CSV实例未转换到COCO: Cone_6_BP_PIPCamera_C_1 RGB(223, 95, 223)"}, {"timestamp": "2025-07-31T13:23:17.992020", "message": "CSV实例未转换到COCO: Cone_6_ExternalCamera RGB(159, 255, 159)"}, {"timestamp": "2025-07-31T13:23:17.992021", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_45 RGB(191, 95, 191)"}, {"timestamp": "2025-07-31T13:23:17.992023", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_left RGB(159, 255, 223)"}, {"timestamp": "2025-07-31T13:23:17.992024", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_43 RGB(191, 95, 255)"}, {"timestamp": "2025-07-31T13:23:17.992026", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_14 RGB(255, 191, 127)"}, {"timestamp": "2025-07-31T13:23:17.992028", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_18 RGB(255, 191, 191)"}, {"timestamp": "2025-07-31T13:23:17.992029", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_13 RGB(255, 191, 255)"}, {"timestamp": "2025-07-31T13:23:17.992030", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_ExternalCamera RGB(159, 191, 95)"}, {"timestamp": "2025-07-31T13:23:17.992032", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_32 RGB(127, 255, 95)"}, {"timestamp": "2025-07-31T13:23:17.992033", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_right RGB(127, 223, 127)"}, {"timestamp": "2025-07-31T13:23:17.992034", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_ExternalCamera RGB(159, 191, 159)"}, {"timestamp": "2025-07-31T13:23:17.992035", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var4_8_InstancedFoliageActor_0 RGB(127, 255, 159)"}, {"timestamp": "2025-07-31T13:23:17.992037", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_right RGB(127, 223, 191)"}, {"timestamp": "2025-07-31T13:23:17.992038", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_left RGB(159, 191, 223)"}, {"timestamp": "2025-07-31T13:23:17.992039", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_center RGB(127, 255, 223)"}, {"timestamp": "2025-07-31T13:23:17.992040", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_right RGB(127, 223, 255)"}, {"timestamp": "2025-07-31T13:23:17.992042", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_driver RGB(159, 223, 255)"}, {"timestamp": "2025-07-31T13:23:17.992043", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_ExternalCamera RGB(159, 95, 127)"}, {"timestamp": "2025-07-31T13:23:17.992044", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_74 RGB(127, 159, 127)"}, {"timestamp": "2025-07-31T13:23:17.992045", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_driver RGB(159, 223, 127)"}, {"timestamp": "2025-07-31T13:23:17.992047", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_ExternalCamera RGB(159, 95, 191)"}, {"timestamp": "2025-07-31T13:23:17.992048", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_75 RGB(127, 159, 191)"}, {"timestamp": "2025-07-31T13:23:17.992049", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_ExternalCamera RGB(159, 95, 255)"}, {"timestamp": "2025-07-31T13:23:17.992050", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_73 RGB(127, 159, 255)"}, {"timestamp": "2025-07-31T13:23:17.992052", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_BP_PIPCamera_C_0 RGB(223, 191, 159)"}, {"timestamp": "2025-07-31T13:23:17.992053", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_BP_PIPCamera_C_1 RGB(223, 191, 223)"}, {"timestamp": "2025-07-31T13:23:17.992054", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_26 RGB(191, 191, 255)"}, {"timestamp": "2025-07-31T13:23:17.992055", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_BP_PIPCamera_C_1 RGB(223, 223, 191)"}, {"timestamp": "2025-07-31T13:23:17.992057", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_back_center RGB(191, 223, 223)"}, {"timestamp": "2025-07-31T13:23:17.992058", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_BP_PIPCamera_C_1 RGB(223, 223, 127)"}, {"timestamp": "2025-07-31T13:23:17.992059", "message": "CSV实例未转换到COCO: coral18_0_StaticMeshActor_81 RGB(191, 159, 95)"}, {"timestamp": "2025-07-31T13:23:17.992061", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_96 RGB(191, 159, 159)"}, {"timestamp": "2025-07-31T13:23:17.992062", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_left RGB(191, 159, 223)"}, {"timestamp": "2025-07-31T13:23:17.992065", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_driver RGB(191, 223, 159)"}, {"timestamp": "2025-07-31T13:23:17.992066", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_right RGB(127, 223, 95)"}, {"timestamp": "2025-07-31T13:23:17.992067", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_back_center RGB(223, 255, 127)"}, {"timestamp": "2025-07-31T13:23:17.992068", "message": "CSV实例未转换到COCO: shayu_0_StaticMeshActor_116 RGB(159, 191, 127)"}, {"timestamp": "2025-07-31T13:23:17.992070", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_right RGB(127, 223, 159)"}, {"timestamp": "2025-07-31T13:23:17.992071", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_back_center RGB(223, 255, 191)"}, {"timestamp": "2025-07-31T13:23:17.992072", "message": "CSV实例未转换到COCO: SM_CineCam_0_ExternalCamera RGB(159, 191, 191)"}, {"timestamp": "2025-07-31T13:23:17.992073", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_back_center RGB(127, 223, 223)"}, {"timestamp": "2025-07-31T13:23:17.992075", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_back_center RGB(223, 255, 255)"}, {"timestamp": "2025-07-31T13:23:17.992076", "message": "CSV实例未转换到COCO: s<PERSON>wangdouyu_0_StaticMeshActor_114 RGB(159, 191, 255)"}, {"timestamp": "2025-07-31T13:23:17.992077", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_ExternalCamera RGB(159, 95, 95)"}, {"timestamp": "2025-07-31T13:23:17.992079", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_BP_PIPCamera_C_0 RGB(223, 191, 127)"}, {"timestamp": "2025-07-31T13:23:17.992081", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_ExternalCamera RGB(159, 95, 159)"}, {"timestamp": "2025-07-31T13:23:17.992082", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_37 RGB(191, 191, 95)"}, {"timestamp": "2025-07-31T13:23:17.992083", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_BP_PIPCamera_C_0 RGB(223, 191, 191)"}, {"timestamp": "2025-07-31T13:23:17.992085", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_63 RGB(191, 191, 159)"}, {"timestamp": "2025-07-31T13:23:17.992086", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_BP_PIPCamera_C_0 RGB(223, 191, 255)"}, {"timestamp": "2025-07-31T13:23:17.992087", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_left RGB(191, 191, 223)"}, {"timestamp": "2025-07-31T13:23:17.992088", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_36 RGB(191, 127, 95)"}, {"timestamp": "2025-07-31T13:23:17.992090", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var12_lod0_3_InstancedFoliageActor_0 RGB(95, 95, 95)"}, {"timestamp": "2025-07-31T13:23:17.992091", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_62 RGB(191, 127, 159)"}, {"timestamp": "2025-07-31T13:23:17.992092", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_68 RGB(95, 95, 159)"}, {"timestamp": "2025-07-31T13:23:17.992094", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_78 RGB(191, 159, 127)"}, {"timestamp": "2025-07-31T13:23:17.992095", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_79 RGB(191, 159, 191)"}, {"timestamp": "2025-07-31T13:23:17.992096", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_center RGB(191, 127, 223)"}, {"timestamp": "2025-07-31T13:23:17.992097", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_left RGB(95, 95, 223)"}, {"timestamp": "2025-07-31T13:23:17.992099", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_77 RGB(191, 159, 255)"}, {"timestamp": "2025-07-31T13:23:17.992100", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_back_center RGB(223, 255, 95)"}, {"timestamp": "2025-07-31T13:23:17.992101", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_back_center RGB(223, 255, 159)"}, {"timestamp": "2025-07-31T13:23:17.992103", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_21 RGB(191, 255, 127)"}, {"timestamp": "2025-07-31T13:23:17.992104", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_BP_PIPCamera_C_1 RGB(223, 255, 223)"}, {"timestamp": "2025-07-31T13:23:17.992105", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_24 RGB(191, 255, 191)"}, {"timestamp": "2025-07-31T13:23:17.992106", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_20 RGB(191, 255, 255)"}, {"timestamp": "2025-07-31T13:23:17.992108", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_driver RGB(191, 223, 95)"}, {"timestamp": "2025-07-31T13:23:17.992109", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_67 RGB(95, 191, 159)"}, {"timestamp": "2025-07-31T13:23:17.992110", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_left RGB(95, 191, 223)"}], "warnings": [{"timestamp": "2025-07-31T13:23:18.036228", "message": "annotation_id=3 bbox精度不足: IoU=0.026"}, {"timestamp": "2025-07-31T13:23:18.036706", "message": "annotation_id=3 分割精度不足: IoU=0.016"}, {"timestamp": "2025-07-31T13:23:18.036826", "message": "annotation_id=3 面积差异过大: COCO=679.5, 实际=46042.0, 差异=98.5%"}, {"timestamp": "2025-07-31T13:23:18.047269", "message": "annotation_id=4 bbox精度不足: IoU=0.716"}, {"timestamp": "2025-07-31T13:23:18.069041", "message": "annotation_id=6 bbox精度不足: IoU=0.360"}, {"timestamp": "2025-07-31T13:23:18.069578", "message": "annotation_id=6 面积差异过大: COCO=401.5, 实际=523.0, 差异=23.2%"}, {"timestamp": "2025-07-31T13:23:18.080063", "message": "annotation_id=7 面积差异过大: COCO=656.0, 实际=779.0, 差异=15.8%"}, {"timestamp": "2025-07-31T13:23:18.090131", "message": "annotation_id=8 bbox精度不足: IoU=0.137"}, {"timestamp": "2025-07-31T13:23:18.090455", "message": "annotation_id=8 分割精度不足: IoU=0.506"}, {"timestamp": "2025-07-31T13:23:18.090568", "message": "annotation_id=8 面积差异过大: COCO=3485.5, 实际=7048.0, 差异=50.5%"}, {"timestamp": "2025-07-31T13:23:18.100526", "message": "annotation_id=9 bbox精度不足: IoU=0.159"}, {"timestamp": "2025-07-31T13:23:18.100964", "message": "annotation_id=9 分割精度不足: IoU=0.481"}, {"timestamp": "2025-07-31T13:23:18.101155", "message": "annotation_id=9 面积差异过大: COCO=3307.0, 实际=7048.0, 差异=53.1%"}], "info": [{"timestamp": "2025-07-31T13:23:17.984991", "message": "成功加载COCO文件: ../output/coco/dataset.json"}, {"timestamp": "2025-07-31T13:23:17.984998", "message": "开始COCO格式规范验证..."}, {"timestamp": "2025-07-31T13:23:17.985060", "message": "开始数据关联性验证..."}, {"timestamp": "2025-07-31T13:23:17.985074", "message": "开始实例分割特异性验证..."}, {"timestamp": "2025-07-31T13:23:17.992129", "message": "开始几何精度验证..."}, {"timestamp": "2025-07-31T13:23:18.113239", "message": "生成可视化图像..."}, {"timestamp": "2025-07-31T13:23:18.502384", "message": "生成验证报告..."}], "statistics": {"csv_instances": 218, "coco_instances": 8, "matched_instances": 8, "missing_instances": 210, "extra_instances": 0, "geometry": {"total_annotations": 10, "bbox_accuracy_issues": 5, "segmentation_issues": 3, "area_mismatches": 5}, "basic": {"total_images": 1, "total_annotations": 10, "total_categories": 9, "avg_annotations_per_image": 10.0}, "categories": {"1": 10}, "areas": {"min_area": 401.5, "max_area": 155059.0, "mean_area": 32501.85, "median_area": 3396.25}}, "validation_time": null}