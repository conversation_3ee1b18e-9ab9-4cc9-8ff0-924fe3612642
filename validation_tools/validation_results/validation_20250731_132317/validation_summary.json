{"timestamp": "2025-07-31T13:23:19.559948", "coco_validation": {"status": "error", "message": "Command '['python', 'validate_coco_dataset.py', '--coco', '../output/coco/dataset.json', '--output', 'validation_results/validation_20250731_132317', '--images', '../.', '--csv', '../airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv', '--config', '/Users/<USER>/autoDlProject/csvToCoco/validation_tools/validation_config.yaml']' returned non-zero exit status 1."}, "mask_validation": {"status": "error", "message": "Command '['python', 'binary_mask_validator.py', '--coco', '../output/coco/dataset.json', '--images', '../.', '--output', 'validation_results/validation_20250731_132317/mask_validation']' returned non-zero exit status 1."}, "performance": {"timestamps": [1753939397.285604, 1753939398.290765], "memory_usage": [64.296875, 64.3125], "cpu_percent": [0.0, 0.2], "max_memory": 64.3125, "avg_memory": 64.3046875, "memory_increase": 0.046875, "max_cpu": 0.2, "avg_cpu": 0.1}}