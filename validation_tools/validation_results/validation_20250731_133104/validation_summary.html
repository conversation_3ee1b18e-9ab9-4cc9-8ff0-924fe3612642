
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COCO数据集验证总结报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; }
        .warning { color: orange; }
        .success { color: green; }
        .metrics { display: flex; flex-wrap: wrap; }
        .metric-card { background-color: #f9f9f9; margin: 10px; padding: 15px; border-radius: 5px; width: 200px; }
        .metric-value { font-size: 24px; font-weight: bold; margin: 10px 0; }
        img { max-width: 100%; height: auto; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>COCO数据集验证总结报告</h1>
        <p>生成时间: 2025-07-31 13:31:07</p>
    </div>
    
    <div class="section">
        <h2>验证概要</h2>
        <div class="metrics">
            <div class="metric-card">
                <h3>COCO格式错误</h3>
                <div class="metric-value success">0</div>
            </div>
            <div class="metric-card">
                <h3>COCO格式警告</h3>
                <div class="metric-value success">0</div>
            </div>
            <div class="metric-card">
                <h3>掩码重建成功率</h3>
                <div class="metric-value warning">80.0%</div>
            </div>
            <div class="metric-card">
                <h3>最大内存使用</h3>
                <div class="metric-value">65.4 MB</div>
            </div>
            <div class="metric-card">
                <h3>平均CPU使用</h3>
                <div class="metric-value">0.1%</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>性能监控</h2>
        <img src="performance_metrics.png" alt="性能监控图表">
    </div>
    
    <div class="section">
        <h2>验证结果</h2>
        <p>详细结果请查看以下文件:</p>
        <ul>
            <li>COCO验证: <code>validation_report.json</code> 和 <code>validation_report.html</code></li>
            <li>掩码验证: <code>mask_validation/mask_validation_results.json</code></li>
            <li>可视化结果: <code>visualizations/</code> 和 <code>mask_validation/</code> 目录</li>
        </ul>
    </div>
</body>
</html>
    