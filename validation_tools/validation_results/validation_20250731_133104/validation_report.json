{"errors": [{"timestamp": "2025-07-31T13:31:05.418787", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_23 RGB(191, 127, 127)"}, {"timestamp": "2025-07-31T13:31:05.418795", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var10_lod0_1_InstancedFoliageActor_0 RGB(95, 95, 127)"}, {"timestamp": "2025-07-31T13:31:05.418797", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_25 RGB(191, 127, 191)"}, {"timestamp": "2025-07-31T13:31:05.418799", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var11_lod0_2_InstancedFoliageActor_0 RGB(95, 95, 191)"}, {"timestamp": "2025-07-31T13:31:05.418800", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_22 RGB(191, 127, 255)"}, {"timestamp": "2025-07-31T13:31:05.418802", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var14_lod0_0_InstancedFoliageActor_0 RGB(95, 95, 255)"}, {"timestamp": "2025-07-31T13:31:05.418803", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_35 RGB(191, 255, 95)"}, {"timestamp": "2025-07-31T13:31:05.418805", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_61 RGB(191, 255, 159)"}, {"timestamp": "2025-07-31T13:31:05.418806", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_center RGB(191, 255, 223)"}, {"timestamp": "2025-07-31T13:31:05.418807", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_50 RGB(95, 255, 127)"}, {"timestamp": "2025-07-31T13:31:05.418809", "message": "CSV实例未转换到COCO: water_0_BP_UnderWaterV3_C_1 RGB(95, 255, 191)"}, {"timestamp": "2025-07-31T13:31:05.418810", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_49 RGB(95, 255, 255)"}, {"timestamp": "2025-07-31T13:31:05.418811", "message": "CSV实例未转换到COCO: coral25_0_StaticMeshActor_92 RGB(95, 159, 95)"}, {"timestamp": "2025-07-31T13:31:05.418813", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_BP_PIPCamera_C_1 RGB(223, 223, 255)"}, {"timestamp": "2025-07-31T13:31:05.418816", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_55 RGB(95, 191, 127)"}, {"timestamp": "2025-07-31T13:31:05.418817", "message": "CSV实例未转换到COCO: SM_Coral_V04_0_StaticMeshActor_0 RGB(95, 159, 159)"}, {"timestamp": "2025-07-31T13:31:05.418818", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_right RGB(191, 223, 127)"}, {"timestamp": "2025-07-31T13:31:05.418820", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_56 RGB(95, 191, 191)"}, {"timestamp": "2025-07-31T13:31:05.418821", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_left RGB(95, 159, 223)"}, {"timestamp": "2025-07-31T13:31:05.418823", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_right RGB(191, 223, 191)"}, {"timestamp": "2025-07-31T13:31:05.418824", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_54 RGB(95, 191, 255)"}, {"timestamp": "2025-07-31T13:31:05.418826", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_right RGB(191, 223, 255)"}, {"timestamp": "2025-07-31T13:31:05.418827", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_72 RGB(255, 159, 95)"}, {"timestamp": "2025-07-31T13:31:05.418828", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_94 RGB(255, 159, 159)"}, {"timestamp": "2025-07-31T13:31:05.418830", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_center RGB(255, 159, 223)"}, {"timestamp": "2025-07-31T13:31:05.418831", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_52 RGB(95, 127, 127)"}, {"timestamp": "2025-07-31T13:31:05.418832", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_51 RGB(95, 127, 255)"}, {"timestamp": "2025-07-31T13:31:05.418834", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_33 RGB(127, 127, 95)"}, {"timestamp": "2025-07-31T13:31:05.418835", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var5_9_InstancedFoliageActor_0 RGB(127, 127, 159)"}, {"timestamp": "2025-07-31T13:31:05.418836", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_center RGB(255, 95, 223)"}, {"timestamp": "2025-07-31T13:31:05.418838", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_center RGB(127, 127, 223)"}, {"timestamp": "2025-07-31T13:31:05.418839", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_BP_PIPCamera_C_0 RGB(223, 191, 95)"}, {"timestamp": "2025-07-31T13:31:05.418840", "message": "CSV实例未转换到COCO: EnviroDome_0_HDRIBackdrop_C_1 RGB(95, 255, 95)"}, {"timestamp": "2025-07-31T13:31:05.418842", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_65 RGB(95, 255, 159)"}, {"timestamp": "2025-07-31T13:31:05.418843", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_left RGB(95, 255, 223)"}, {"timestamp": "2025-07-31T13:31:05.418844", "message": "CSV实例未转换到COCO: StaticMeshActor_90 RGB(95, 159, 127)"}, {"timestamp": "2025-07-31T13:31:05.418846", "message": "CSV实例未转换到COCO: StaticMeshActor_91 RGB(95, 159, 191)"}, {"timestamp": "2025-07-31T13:31:05.418847", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_right RGB(255, 223, 127)"}, {"timestamp": "2025-07-31T13:31:05.418848", "message": "CSV实例未转换到COCO: StaticMeshActor_88 RGB(95, 159, 255)"}, {"timestamp": "2025-07-31T13:31:05.418850", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_right RGB(255, 223, 191)"}, {"timestamp": "2025-07-31T13:31:05.418851", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_right RGB(255, 223, 255)"}, {"timestamp": "2025-07-31T13:31:05.418853", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_30 RGB(255, 127, 95)"}, {"timestamp": "2025-07-31T13:31:05.418854", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_gpulidar_gpulidar RGB(255, 255, 79)"}, {"timestamp": "2025-07-31T13:31:05.418855", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var1_5_InstancedFoliageActor_0 RGB(255, 127, 159)"}, {"timestamp": "2025-07-31T13:31:05.418857", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_70 RGB(255, 159, 127)"}, {"timestamp": "2025-07-31T13:31:05.418858", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_71 RGB(255, 159, 191)"}, {"timestamp": "2025-07-31T13:31:05.418859", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_center RGB(255, 127, 223)"}, {"timestamp": "2025-07-31T13:31:05.418860", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_69 RGB(255, 159, 255)"}, {"timestamp": "2025-07-31T13:31:05.418862", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_66 RGB(95, 127, 159)"}, {"timestamp": "2025-07-31T13:31:05.418863", "message": "CSV实例未转换到COCO: airsimvehicle RGB(159, 159, 95)"}, {"timestamp": "2025-07-31T13:31:05.418866", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_17 RGB(127, 191, 127)"}, {"timestamp": "2025-07-31T13:31:05.418867", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_left RGB(95, 127, 223)"}, {"timestamp": "2025-07-31T13:31:05.418868", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_center RGB(159, 159, 159)"}, {"timestamp": "2025-07-31T13:31:05.418870", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_19 RGB(127, 191, 191)"}, {"timestamp": "2025-07-31T13:31:05.418871", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_left RGB(159, 159, 223)"}, {"timestamp": "2025-07-31T13:31:05.418872", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_16 RGB(127, 191, 255)"}, {"timestamp": "2025-07-31T13:31:05.418874", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_47 RGB(127, 95, 95)"}, {"timestamp": "2025-07-31T13:31:05.418875", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_BP_PIPCamera_C_1 RGB(223, 159, 95)"}, {"timestamp": "2025-07-31T13:31:05.418876", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_8 RGB(127, 127, 127)"}, {"timestamp": "2025-07-31T13:31:05.418878", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_60 RGB(127, 95, 159)"}, {"timestamp": "2025-07-31T13:31:05.418879", "message": "CSV实例未转换到COCO: coral25_0_StaticMeshActor_41 RGB(255, 95, 191)"}, {"timestamp": "2025-07-31T13:31:05.418880", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_12 RGB(127, 127, 191)"}, {"timestamp": "2025-07-31T13:31:05.418882", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_center RGB(127, 95, 223)"}, {"timestamp": "2025-07-31T13:31:05.418883", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_7 RGB(127, 127, 255)"}, {"timestamp": "2025-07-31T13:31:05.418885", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_BP_PIPCamera_C_1 RGB(223, 159, 159)"}, {"timestamp": "2025-07-31T13:31:05.418886", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_BP_PIPCamera_C_1 RGB(223, 159, 223)"}, {"timestamp": "2025-07-31T13:31:05.418887", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_gpulidar_gpulidar RGB(255, 127, 79)"}, {"timestamp": "2025-07-31T13:31:05.418889", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_2 RGB(255, 255, 127)"}, {"timestamp": "2025-07-31T13:31:05.418890", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_9 RGB(255, 255, 191)"}, {"timestamp": "2025-07-31T13:31:05.418891", "message": "CSV实例未转换到COCO: SM_Template_Map_Floor_0_Floor RGB(255, 255, 255)"}, {"timestamp": "2025-07-31T13:31:05.419412", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_driver RGB(95, 223, 95)"}, {"timestamp": "2025-07-31T13:31:05.419414", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_driver RGB(95, 223, 159)"}, {"timestamp": "2025-07-31T13:31:05.419416", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_back_center RGB(95, 223, 223)"}, {"timestamp": "2025-07-31T13:31:05.419417", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_right RGB(255, 223, 95)"}, {"timestamp": "2025-07-31T13:31:05.419418", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_BP_PIPCamera_C_0 RGB(223, 95, 191)"}, {"timestamp": "2025-07-31T13:31:05.419420", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_front_right RGB(255, 223, 159)"}, {"timestamp": "2025-07-31T13:31:05.419421", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_driver RGB(255, 223, 223)"}, {"timestamp": "2025-07-31T13:31:05.419423", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_4 RGB(255, 127, 127)"}, {"timestamp": "2025-07-31T13:31:05.419424", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_10 RGB(255, 127, 191)"}, {"timestamp": "2025-07-31T13:31:05.419426", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_3 RGB(255, 127, 255)"}, {"timestamp": "2025-07-31T13:31:05.419427", "message": "CSV实例未转换到COCO: shuangdaigongziaohei_0_StaticMeshActor_112 RGB(159, 127, 95)"}, {"timestamp": "2025-07-31T13:31:05.419428", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_34 RGB(127, 191, 95)"}, {"timestamp": "2025-07-31T13:31:05.419430", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_ExternalCamera RGB(159, 127, 159)"}, {"timestamp": "2025-07-31T13:31:05.419431", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_59 RGB(127, 191, 159)"}, {"timestamp": "2025-07-31T13:31:05.419432", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_ExternalCamera RGB(159, 159, 127)"}, {"timestamp": "2025-07-31T13:31:05.419434", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_ExternalCamera RGB(159, 159, 191)"}, {"timestamp": "2025-07-31T13:31:05.419435", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_center RGB(127, 191, 223)"}, {"timestamp": "2025-07-31T13:31:05.419436", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_left RGB(159, 127, 223)"}, {"timestamp": "2025-07-31T13:31:05.419438", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_ExternalCamera RGB(159, 159, 255)"}, {"timestamp": "2025-07-31T13:31:05.419439", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_46 RGB(255, 95, 95)"}, {"timestamp": "2025-07-31T13:31:05.419440", "message": "CSV实例未转换到COCO: SM_CineCam_1_CineCameraActor_0 RGB(127, 95, 127)"}, {"timestamp": "2025-07-31T13:31:05.419442", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_back_center RGB(223, 127, 95)"}, {"timestamp": "2025-07-31T13:31:05.419443", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var3_7_InstancedFoliageActor_0 RGB(255, 95, 159)"}, {"timestamp": "2025-07-31T13:31:05.419447", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_42 RGB(127, 95, 191)"}, {"timestamp": "2025-07-31T13:31:05.419448", "message": "CSV实例未转换到COCO: SM_CineCam_0_BP_PIPCamera_C_0 RGB(223, 127, 159)"}, {"timestamp": "2025-07-31T13:31:05.419450", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_BP_PIPCamera_C_0 RGB(223, 159, 127)"}, {"timestamp": "2025-07-31T13:31:05.419451", "message": "CSV实例未转换到COCO: SM_CineCam_0_CineCameraActor_0 RGB(127, 95, 255)"}, {"timestamp": "2025-07-31T13:31:05.419452", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_BP_PIPCamera_C_0 RGB(223, 159, 255)"}, {"timestamp": "2025-07-31T13:31:05.419453", "message": "CSV实例未转换到COCO: SM_CineCam_0_BP_PIPCamera_C_1 RGB(223, 159, 191)"}, {"timestamp": "2025-07-31T13:31:05.419455", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_BP_PIPCamera_C_1 RGB(223, 127, 223)"}, {"timestamp": "2025-07-31T13:31:05.419458", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var13_lod0_4_InstancedFoliageActor_0 RGB(255, 255, 159)"}, {"timestamp": "2025-07-31T13:31:05.419459", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_center RGB(255, 255, 223)"}, {"timestamp": "2025-07-31T13:31:05.419460", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_BP_PIPCamera_C_0 RGB(223, 95, 127)"}, {"timestamp": "2025-07-31T13:31:05.419462", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_driver RGB(95, 223, 127)"}, {"timestamp": "2025-07-31T13:31:05.419463", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_48 RGB(191, 95, 95)"}, {"timestamp": "2025-07-31T13:31:05.419464", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_driver RGB(95, 223, 191)"}, {"timestamp": "2025-07-31T13:31:05.419465", "message": "CSV实例未转换到COCO: tongshiguiyu_0_StaticMeshActor_93 RGB(159, 255, 127)"}, {"timestamp": "2025-07-31T13:31:05.419467", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_64 RGB(191, 95, 159)"}, {"timestamp": "2025-07-31T13:31:05.419468", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_driver RGB(95, 223, 255)"}, {"timestamp": "2025-07-31T13:31:05.419470", "message": "CSV实例未转换到COCO: shitoujinyu_0_StaticMeshActor_98 RGB(159, 255, 191)"}, {"timestamp": "2025-07-31T13:31:05.419471", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_front_left RGB(191, 95, 223)"}, {"timestamp": "2025-07-31T13:31:05.419472", "message": "CSV实例未转换到COCO: Cone_6_BP_PIPCamera_C_0 RGB(223, 95, 255)"}, {"timestamp": "2025-07-31T13:31:05.419473", "message": "CSV实例未转换到COCO: base_basic_shaded_0_StaticMeshActor_15 RGB(159, 255, 255)"}, {"timestamp": "2025-07-31T13:31:05.419475", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_31 RGB(255, 191, 95)"}, {"timestamp": "2025-07-31T13:31:05.419476", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var2_6_InstancedFoliageActor_0 RGB(255, 191, 159)"}, {"timestamp": "2025-07-31T13:31:05.419477", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_front_center RGB(255, 191, 223)"}, {"timestamp": "2025-07-31T13:31:05.419479", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_driver RGB(159, 223, 95)"}, {"timestamp": "2025-07-31T13:31:05.419480", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_6 RGB(127, 255, 127)"}, {"timestamp": "2025-07-31T13:31:05.419481", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_driver RGB(159, 223, 159)"}, {"timestamp": "2025-07-31T13:31:05.419482", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_11 RGB(127, 255, 191)"}, {"timestamp": "2025-07-31T13:31:05.419484", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_airsimvehicle_camera_back_center RGB(159, 223, 223)"}, {"timestamp": "2025-07-31T13:31:05.419485", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_5 RGB(127, 255, 255)"}, {"timestamp": "2025-07-31T13:31:05.419486", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_76 RGB(127, 159, 95)"}, {"timestamp": "2025-07-31T13:31:05.419487", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_BP_PIPCamera_C_1 RGB(223, 223, 95)"}, {"timestamp": "2025-07-31T13:31:05.419489", "message": "CSV实例未转换到COCO: chixuehonglongye_0_StaticMeshActor_108 RGB(159, 127, 127)"}, {"timestamp": "2025-07-31T13:31:05.419490", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_95 RGB(127, 159, 159)"}, {"timestamp": "2025-07-31T13:31:05.419491", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_BP_PIPCamera_C_1 RGB(223, 223, 159)"}, {"timestamp": "2025-07-31T13:31:05.419493", "message": "CSV实例未转换到COCO: yinnihuyu_0_StaticMeshActor_110 RGB(159, 127, 191)"}, {"timestamp": "2025-07-31T13:31:05.419494", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_center RGB(127, 159, 223)"}, {"timestamp": "2025-07-31T13:31:05.419495", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_front_left RGB(159, 95, 223)"}, {"timestamp": "2025-07-31T13:31:05.419497", "message": "CSV实例未转换到COCO: niluokoufeiji_0_StaticMeshActor_104 RGB(159, 127, 255)"}, {"timestamp": "2025-07-31T13:31:05.419498", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_driver RGB(159, 223, 191)"}, {"timestamp": "2025-07-31T13:31:05.419499", "message": "CSV实例未转换到COCO: MatineeCam_SM_0_airsimvehicle_gpulidar_gpulidar RGB(223, 223, 223)"}, {"timestamp": "2025-07-31T13:31:05.419501", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_back_center RGB(223, 127, 127)"}, {"timestamp": "2025-07-31T13:31:05.419502", "message": "CSV实例未转换到COCO: MatineeCam_SM_11_airsimvehicle_camera_back_center RGB(223, 127, 191)"}, {"timestamp": "2025-07-31T13:31:05.419503", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_back_center RGB(223, 127, 255)"}, {"timestamp": "2025-07-31T13:31:05.419504", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_BP_PIPCamera_C_0 RGB(223, 95, 95)"}, {"timestamp": "2025-07-31T13:31:05.419506", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_BP_PIPCamera_C_0 RGB(223, 95, 159)"}, {"timestamp": "2025-07-31T13:31:05.419507", "message": "CSV实例未转换到COCO: hongdidiao_0_StaticMeshActor_100 RGB(159, 255, 95)"}, {"timestamp": "2025-07-31T13:31:05.419508", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_44 RGB(191, 95, 127)"}, {"timestamp": "2025-07-31T13:31:05.419510", "message": "CSV实例未转换到COCO: Cone_6_BP_PIPCamera_C_1 RGB(223, 95, 223)"}, {"timestamp": "2025-07-31T13:31:05.419511", "message": "CSV实例未转换到COCO: Cone_6_ExternalCamera RGB(159, 255, 159)"}, {"timestamp": "2025-07-31T13:31:05.419512", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_45 RGB(191, 95, 191)"}, {"timestamp": "2025-07-31T13:31:05.419514", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_left RGB(159, 255, 223)"}, {"timestamp": "2025-07-31T13:31:05.419515", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_43 RGB(191, 95, 255)"}, {"timestamp": "2025-07-31T13:31:05.419517", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_14 RGB(255, 191, 127)"}, {"timestamp": "2025-07-31T13:31:05.419519", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_18 RGB(255, 191, 191)"}, {"timestamp": "2025-07-31T13:31:05.419520", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_13 RGB(255, 191, 255)"}, {"timestamp": "2025-07-31T13:31:05.419521", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_ExternalCamera RGB(159, 191, 95)"}, {"timestamp": "2025-07-31T13:31:05.419523", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_32 RGB(127, 255, 95)"}, {"timestamp": "2025-07-31T13:31:05.419524", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_right RGB(127, 223, 127)"}, {"timestamp": "2025-07-31T13:31:05.419525", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_ExternalCamera RGB(159, 191, 159)"}, {"timestamp": "2025-07-31T13:31:05.419526", "message": "CSV实例未转换到COCO: S_Thai_Beach_Corals_Pack_uf3kbggfa_lod3_Var4_8_InstancedFoliageActor_0 RGB(127, 255, 159)"}, {"timestamp": "2025-07-31T13:31:05.419528", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_front_right RGB(127, 223, 191)"}, {"timestamp": "2025-07-31T13:31:05.419529", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_airsimvehicle_camera_front_left RGB(159, 191, 223)"}, {"timestamp": "2025-07-31T13:31:05.419530", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_center RGB(127, 255, 223)"}, {"timestamp": "2025-07-31T13:31:05.419532", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_right RGB(127, 223, 255)"}, {"timestamp": "2025-07-31T13:31:05.419533", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_driver RGB(159, 223, 255)"}, {"timestamp": "2025-07-31T13:31:05.419534", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_ExternalCamera RGB(159, 95, 127)"}, {"timestamp": "2025-07-31T13:31:05.419535", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_74 RGB(127, 159, 127)"}, {"timestamp": "2025-07-31T13:31:05.419537", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_driver RGB(159, 223, 127)"}, {"timestamp": "2025-07-31T13:31:05.419538", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_ExternalCamera RGB(159, 95, 191)"}, {"timestamp": "2025-07-31T13:31:05.419539", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_75 RGB(127, 159, 191)"}, {"timestamp": "2025-07-31T13:31:05.419540", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_ExternalCamera RGB(159, 95, 255)"}, {"timestamp": "2025-07-31T13:31:05.419542", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_73 RGB(127, 159, 255)"}, {"timestamp": "2025-07-31T13:31:05.419543", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_BP_PIPCamera_C_0 RGB(223, 191, 159)"}, {"timestamp": "2025-07-31T13:31:05.419544", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_BP_PIPCamera_C_1 RGB(223, 191, 223)"}, {"timestamp": "2025-07-31T13:31:05.419545", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_26 RGB(191, 191, 255)"}, {"timestamp": "2025-07-31T13:31:05.419547", "message": "CSV实例未转换到COCO: MatineeCam_SM_10_BP_PIPCamera_C_1 RGB(223, 223, 191)"}, {"timestamp": "2025-07-31T13:31:05.419548", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_back_center RGB(191, 223, 223)"}, {"timestamp": "2025-07-31T13:31:05.419549", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_BP_PIPCamera_C_1 RGB(223, 223, 127)"}, {"timestamp": "2025-07-31T13:31:05.419551", "message": "CSV实例未转换到COCO: coral18_0_StaticMeshActor_81 RGB(191, 159, 95)"}, {"timestamp": "2025-07-31T13:31:05.419552", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_96 RGB(191, 159, 159)"}, {"timestamp": "2025-07-31T13:31:05.419553", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_airsimvehicle_camera_front_left RGB(191, 159, 223)"}, {"timestamp": "2025-07-31T13:31:05.419556", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_airsimvehicle_camera_driver RGB(191, 223, 159)"}, {"timestamp": "2025-07-31T13:31:05.419557", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_front_right RGB(127, 223, 95)"}, {"timestamp": "2025-07-31T13:31:05.419559", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_back_center RGB(223, 255, 127)"}, {"timestamp": "2025-07-31T13:31:05.419560", "message": "CSV实例未转换到COCO: shayu_0_StaticMeshActor_116 RGB(159, 191, 127)"}, {"timestamp": "2025-07-31T13:31:05.419561", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_airsimvehicle_camera_front_right RGB(127, 223, 159)"}, {"timestamp": "2025-07-31T13:31:05.419562", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_back_center RGB(223, 255, 191)"}, {"timestamp": "2025-07-31T13:31:05.419564", "message": "CSV实例未转换到COCO: SM_CineCam_0_ExternalCamera RGB(159, 191, 191)"}, {"timestamp": "2025-07-31T13:31:05.419565", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_back_center RGB(127, 223, 223)"}, {"timestamp": "2025-07-31T13:31:05.419566", "message": "CSV实例未转换到COCO: MatineeCam_SM_4_airsimvehicle_camera_back_center RGB(223, 255, 255)"}, {"timestamp": "2025-07-31T13:31:05.419568", "message": "CSV实例未转换到COCO: s<PERSON>wangdouyu_0_StaticMeshActor_114 RGB(159, 191, 255)"}, {"timestamp": "2025-07-31T13:31:05.419569", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_ExternalCamera RGB(159, 95, 95)"}, {"timestamp": "2025-07-31T13:31:05.419571", "message": "CSV实例未转换到COCO: MatineeCam_SM_2_BP_PIPCamera_C_0 RGB(223, 191, 127)"}, {"timestamp": "2025-07-31T13:31:05.419573", "message": "CSV实例未转换到COCO: MatineeCam_SM_9_ExternalCamera RGB(159, 95, 159)"}, {"timestamp": "2025-07-31T13:31:05.419574", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_37 RGB(191, 191, 95)"}, {"timestamp": "2025-07-31T13:31:05.419575", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_BP_PIPCamera_C_0 RGB(223, 191, 191)"}, {"timestamp": "2025-07-31T13:31:05.419576", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_63 RGB(191, 191, 159)"}, {"timestamp": "2025-07-31T13:31:05.419578", "message": "CSV实例未转换到COCO: MatineeCam_SM_1_BP_PIPCamera_C_0 RGB(223, 191, 255)"}, {"timestamp": "2025-07-31T13:31:05.419579", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_front_left RGB(191, 191, 223)"}, {"timestamp": "2025-07-31T13:31:05.419580", "message": "CSV实例未转换到COCO: S_Nordic_Beach_Rocky_Ground_ukohbbi_lod0_0_StaticMeshActor_36 RGB(191, 127, 95)"}, {"timestamp": "2025-07-31T13:31:05.419582", "message": "CSV实例未转换到COCO: S_Wild_Grass_vlkhcbxia_Var12_lod0_3_InstancedFoliageActor_0 RGB(95, 95, 95)"}, {"timestamp": "2025-07-31T13:31:05.419583", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_62 RGB(191, 127, 159)"}, {"timestamp": "2025-07-31T13:31:05.419584", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_68 RGB(95, 95, 159)"}, {"timestamp": "2025-07-31T13:31:05.419585", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_78 RGB(191, 159, 127)"}, {"timestamp": "2025-07-31T13:31:05.419587", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_79 RGB(191, 159, 191)"}, {"timestamp": "2025-07-31T13:31:05.419588", "message": "CSV实例未转换到COCO: MatineeCam_SM_12_airsimvehicle_camera_front_center RGB(191, 127, 223)"}, {"timestamp": "2025-07-31T13:31:05.419589", "message": "CSV实例未转换到COCO: Cone_6_airsimvehicle_camera_front_left RGB(95, 95, 223)"}, {"timestamp": "2025-07-31T13:31:05.419591", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_77 RGB(191, 159, 255)"}, {"timestamp": "2025-07-31T13:31:05.419592", "message": "CSV实例未转换到COCO: MatineeCam_SM_7_airsimvehicle_camera_back_center RGB(223, 255, 95)"}, {"timestamp": "2025-07-31T13:31:05.419593", "message": "CSV实例未转换到COCO: MatineeCam_SM_8_airsimvehicle_camera_back_center RGB(223, 255, 159)"}, {"timestamp": "2025-07-31T13:31:05.419594", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_21 RGB(191, 255, 127)"}, {"timestamp": "2025-07-31T13:31:05.419596", "message": "CSV实例未转换到COCO: MatineeCam_SM_3_BP_PIPCamera_C_1 RGB(223, 255, 223)"}, {"timestamp": "2025-07-31T13:31:05.419597", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_24 RGB(191, 255, 191)"}, {"timestamp": "2025-07-31T13:31:05.419598", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_20 RGB(191, 255, 255)"}, {"timestamp": "2025-07-31T13:31:05.419600", "message": "CSV实例未转换到COCO: SM_CineCam_0_airsimvehicle_camera_driver RGB(191, 223, 95)"}, {"timestamp": "2025-07-31T13:31:05.419601", "message": "CSV实例未转换到COCO: S_Beach_Boulder_wfliedybw_high_Var1_0_StaticMeshActor_67 RGB(95, 191, 159)"}, {"timestamp": "2025-07-31T13:31:05.419602", "message": "CSV实例未转换到COCO: MatineeCam_SM_5_airsimvehicle_camera_front_left RGB(95, 191, 223)"}], "warnings": [{"timestamp": "2025-07-31T13:31:05.461996", "message": "annotation_id=3 bbox精度不足: IoU=0.026"}, {"timestamp": "2025-07-31T13:31:05.462430", "message": "annotation_id=3 分割精度不足: IoU=0.016"}, {"timestamp": "2025-07-31T13:31:05.462548", "message": "annotation_id=3 面积差异过大: COCO=679.5, 实际=46042.0, 差异=98.5%"}, {"timestamp": "2025-07-31T13:31:05.473118", "message": "annotation_id=4 bbox精度不足: IoU=0.716"}, {"timestamp": "2025-07-31T13:31:05.495474", "message": "annotation_id=6 bbox精度不足: IoU=0.360"}, {"timestamp": "2025-07-31T13:31:05.495966", "message": "annotation_id=6 面积差异过大: COCO=401.5, 实际=523.0, 差异=23.2%"}, {"timestamp": "2025-07-31T13:31:05.506718", "message": "annotation_id=7 面积差异过大: COCO=656.0, 实际=779.0, 差异=15.8%"}, {"timestamp": "2025-07-31T13:31:05.517305", "message": "annotation_id=8 bbox精度不足: IoU=0.137"}, {"timestamp": "2025-07-31T13:31:05.517656", "message": "annotation_id=8 分割精度不足: IoU=0.506"}, {"timestamp": "2025-07-31T13:31:05.517768", "message": "annotation_id=8 面积差异过大: COCO=3485.5, 实际=7048.0, 差异=50.5%"}, {"timestamp": "2025-07-31T13:31:05.528031", "message": "annotation_id=9 bbox精度不足: IoU=0.159"}, {"timestamp": "2025-07-31T13:31:05.528638", "message": "annotation_id=9 分割精度不足: IoU=0.481"}, {"timestamp": "2025-07-31T13:31:05.528934", "message": "annotation_id=9 面积差异过大: COCO=3307.0, 实际=7048.0, 差异=53.1%"}], "info": [{"timestamp": "2025-07-31T13:31:05.409758", "message": "成功加载COCO文件: ../output/coco/dataset.json"}, {"timestamp": "2025-07-31T13:31:05.409779", "message": "开始COCO格式规范验证..."}, {"timestamp": "2025-07-31T13:31:05.409857", "message": "开始数据关联性验证..."}, {"timestamp": "2025-07-31T13:31:05.409885", "message": "开始实例分割特异性验证..."}, {"timestamp": "2025-07-31T13:31:05.419622", "message": "开始几何精度验证..."}, {"timestamp": "2025-07-31T13:31:05.541409", "message": "生成可视化图像..."}, {"timestamp": "2025-07-31T13:31:05.926678", "message": "生成验证报告..."}], "statistics": {"csv_instances": 218, "coco_instances": 8, "matched_instances": 8, "missing_instances": 210, "extra_instances": 0, "geometry": {"total_annotations": 10, "bbox_accuracy_issues": 5, "segmentation_issues": 3, "area_mismatches": 5}, "basic": {"total_images": 1, "total_annotations": 10, "total_categories": 9, "avg_annotations_per_image": 10.0}, "categories": {"1": 10}, "areas": {"min_area": 401.5, "max_area": 155059.0, "mean_area": 32501.85, "median_area": 3396.25}}, "validation_time": null}