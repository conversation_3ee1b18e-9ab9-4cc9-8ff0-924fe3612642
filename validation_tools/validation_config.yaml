# COCO数据集验证配置文件
# 配置文件版本: 1.0

# 基本验证设置
validation:
  strict_mode: true                    # 严格模式，启用所有验证规则
  check_geometry: true                 # 启用几何精度验证
  generate_visualizations: true       # 生成可视化图像
  output_dir: "validation_output"      # 输出目录

# 几何验证参数
geometry:
  max_polygon_points: 1000            # 多边形最大点数
  min_area_threshold: 10              # 最小面积阈值（像素）
  bbox_tolerance: 1.0                 # 边界框容差（像素）
  iou_threshold: 0.8                  # IoU阈值，低于此值将产生警告
  area_diff_threshold: 10             # 面积差异阈值（百分比）

# 可视化设置
visualization:
  max_images: 5                       # 最大可视化图像数
  figure_size: [18, 6]               # 图像尺寸 [宽, 高]
  dpi: 150                           # 图像分辨率
  colors:
    bbox: "red"                      # 边界框颜色
    segmentation: "blue"             # 分割掩码颜色
    text: "red"                      # 文本颜色

# 报告设置
report:
  generate_json: true                 # 生成JSON格式报告
  generate_html: true                 # 生成HTML格式报告
  include_timestamps: true            # 包含时间戳
  max_errors_display: 10              # 报告中显示的最大错误数
  max_warnings_display: 5             # 报告中显示的最大警告数

# 实例分割验证设置
instance_segmentation:
  check_name_consistency: true        # 检查实例名称一致性
  check_color_consistency: true       # 检查颜色一致性
  allow_extra_instances: false        # 是否允许COCO中有额外实例
  require_instance_fields: true       # 要求instance_name和instance_color字段

# 掩码生成设置
mask_generation:
  generate_instance_masks: true       # 生成实例级二值掩码
  generate_category_masks: true       # 生成类别级聚合掩码
  generate_comparisons: true          # 生成对比可视化
  output_structure:
    instance_dir: "instance_masks"    # 实例掩码输出目录
    category_dir: "category_masks"    # 类别掩码输出目录
    comparison_dir: "comparisons"     # 对比图输出目录
  visualization:
    show_statistics: true             # 在可视化中显示统计信息
    max_categories_per_row: 4         # 每行最大类别数
    figure_dpi: 150                   # 图像分辨率

# 性能设置
performance:
  max_memory_usage: "2GB"            # 最大内存使用量
  timeout_seconds: 300               # 验证超时时间（秒）
  parallel_processing: false         # 是否启用并行处理

# 日志设置
logging:
  level: "INFO"                      # 日志级别: DEBUG, INFO, WARNING, ERROR
  save_to_file: true                 # 保存日志到文件
  log_file: "validation.log"         # 日志文件名
