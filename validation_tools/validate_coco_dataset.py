#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COCO数据集验证主脚本

使用方法:
python validate_coco_dataset.py --coco output/coco/dataset.json --images . --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv
"""

import argparse
import os
import sys
import yaml
from coco_validator import COCOValidator


def load_validation_config(config_path: str = None) -> dict:
    """加载验证配置"""
    default_config = {
        'strict_mode': True,
        'check_geometry': True,
        'generate_visualizations': True,
        'max_polygon_points': 1000,
        'min_area_threshold': 10,
        'bbox_tolerance': 1.0,
        'iou_threshold': 0.8,
        'output_dir': 'validation_output'
    }
    
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = yaml.safe_load(f)
            default_config.update(user_config)
        except Exception as e:
            print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
    
    return default_config


def print_validation_summary(results: dict) -> None:
    """打印验证结果摘要"""
    print("\n" + "="*60)
    print("🔍 COCO数据集验证结果")
    print("="*60)
    
    # 验证状态
    error_count = len(results['errors'])
    warning_count = len(results['warnings'])
    info_count = len(results['info'])
    
    if error_count == 0:
        print("✅ 验证状态: 通过")
    else:
        print("❌ 验证状态: 失败")
    
    print(f"📊 统计: {error_count} 个错误, {warning_count} 个警告, {info_count} 条信息")
    print(f"⏱️ 验证耗时: {results.get('validation_time', 0):.2f} 秒")
    
    # 数据集统计
    stats = results.get('statistics', {})
    if 'basic' in stats:
        basic = stats['basic']
        print(f"\n📈 数据集统计:")
        print(f"   图像数: {basic.get('total_images', 0)}")
        print(f"   标注数: {basic.get('total_annotations', 0)}")
        print(f"   类别数: {basic.get('total_categories', 0)}")
        print(f"   平均每图标注数: {basic.get('avg_annotations_per_image', 0):.1f}")
    
    # 实例分割统计
    if 'csv_instances' in stats:
        print(f"\n🎯 实例分割统计:")
        print(f"   CSV实例数: {stats.get('csv_instances', 0)}")
        print(f"   COCO实例数: {stats.get('coco_instances', 0)}")
        print(f"   匹配实例数: {stats.get('matched_instances', 0)}")
        print(f"   缺失实例数: {stats.get('missing_instances', 0)}")
        print(f"   额外实例数: {stats.get('extra_instances', 0)}")
    
    # 几何验证统计
    if 'geometry' in stats:
        geo = stats['geometry']
        print(f"\n📐 几何验证统计:")
        print(f"   总标注数: {geo.get('total_annotations', 0)}")
        print(f"   边界框问题: {geo.get('bbox_accuracy_issues', 0)}")
        print(f"   分割问题: {geo.get('segmentation_issues', 0)}")
        print(f"   面积不匹配: {geo.get('area_mismatches', 0)}")
    
    # 显示前几个错误
    if results['errors']:
        print(f"\n❌ 主要错误 (前5个):")
        for i, error in enumerate(results['errors'][:5]):
            print(f"   {i+1}. {error['message']}")
        if len(results['errors']) > 5:
            print(f"   ... 还有 {len(results['errors']) - 5} 个错误")
    
    # 显示前几个警告
    if results['warnings']:
        print(f"\n⚠️ 主要警告 (前3个):")
        for i, warning in enumerate(results['warnings'][:3]):
            print(f"   {i+1}. {warning['message']}")
        if len(results['warnings']) > 3:
            print(f"   ... 还有 {len(results['warnings']) - 3} 个警告")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='COCO数据集验证工具')
    parser.add_argument('--coco', required=True, help='COCO JSON文件路径')
    parser.add_argument('--images', help='图像目录路径')
    parser.add_argument('--csv', help='原始CSV文件路径（用于实例分割验证）')
    parser.add_argument('--config', help='验证配置文件路径')
    parser.add_argument('--output', default='validation_output', help='输出目录')
    parser.add_argument('--strict', action='store_true', help='启用严格模式')
    parser.add_argument('--no-viz', action='store_true', help='禁用可视化生成')
    parser.add_argument('--no-geometry', action='store_true', help='禁用几何验证')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.coco):
        print(f"❌ COCO文件不存在: {args.coco}")
        sys.exit(1)
    
    if args.images and not os.path.exists(args.images):
        print(f"❌ 图像目录不存在: {args.images}")
        sys.exit(1)
    
    if args.csv and not os.path.exists(args.csv):
        print(f"❌ CSV文件不存在: {args.csv}")
        sys.exit(1)
    
    # 加载配置
    config = load_validation_config(args.config)
    
    # 更新配置
    config['output_dir'] = args.output
    if args.strict:
        config['strict_mode'] = True
    if args.no_viz:
        config['generate_visualizations'] = False
    if args.no_geometry:
        config['check_geometry'] = False
    
    print("🚀 开始COCO数据集验证...")
    print(f"📁 COCO文件: {args.coco}")
    if args.images:
        print(f"🖼️ 图像目录: {args.images}")
    if args.csv:
        print(f"📊 CSV文件: {args.csv}")
    print(f"📂 输出目录: {args.output}")
    
    # 创建验证器
    validator = COCOValidator(config)
    
    # 执行验证
    try:
        results = validator.validate_coco_dataset(
            coco_path=args.coco,
            images_dir=args.images,
            csv_path=args.csv
        )
        
        # 打印结果摘要
        print_validation_summary(results)
        
        # 输出文件信息
        print(f"\n📄 详细报告已保存到: {args.output}/")
        print(f"   - validation_report.json (JSON格式)")
        print(f"   - validation_report.html (HTML格式)")
        if config['generate_visualizations']:
            print(f"   - visualizations/ (可视化图像)")
        
        # 返回适当的退出码
        if results['errors']:
            print(f"\n❌ 验证失败: 发现 {len(results['errors'])} 个错误")
            sys.exit(1)
        elif results['warnings']:
            print(f"\n⚠️ 验证通过但有警告: {len(results['warnings'])} 个警告")
            sys.exit(0)
        else:
            print(f"\n✅ 验证完全通过!")
            sys.exit(0)
            
    except Exception as e:
        print(f"\n💥 验证过程失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
