# AirSim COCO数据集生成器 v2.0

## 🎯 项目概述

这是一个全新重写的AirSim语义分割数据处理工具，基于配置文件系统，支持灵活的目标检测和COCO数据集生成。

### ✨ 主要特性

- **🔧 配置文件系统**：YAML/JSON配置，支持灵活的参数设置
- **🎯 智能目标检测**：基于关键词的目标类别筛选
- **📊 详细统计分析**：处理前后的完整统计信息
- **🎨 可视化生成**：自动生成检测结果可视化图像
- **📋 完整报告**：生成详细的处理报告和统计数据
- **🌍 中文支持**：完全支持中文显示和处理
- **⚡ 批量处理**：支持单张图像和批量处理模式
- **✂️ 数据集划分**：自动划分训练/验证/测试集

## 📁 文件结构

```
AirSim COCO Generator v2.0/
├── airsim_coco_generator.py      # 主处理脚本
├── config_template.yaml          # 配置文件模板
├── example_usage_v2.py           # 使用示例脚本
├── requirements_v2.txt           # 依赖包列表
├── README_v2.md                  # 使用说明（本文档）
├── validate.py                   # 验证工具启动脚本
├── validation_tools/             # 验证工具套件
│   ├── README.md                 # 验证工具说明
│   ├── VALIDATION_GUIDE.md       # 详细验证指南
│   ├── coco_validator.py         # 核心验证器
│   ├── validate_coco_dataset.py  # COCO格式验证
│   ├── binary_mask_validator.py  # 二值掩码验证
│   ├── run_validation.py         # 自动化验证框架
│   ├── validation_config.yaml    # 验证配置文件
│   └── validation_results/       # 验证结果输出
└── output/                       # 输出目录
    ├── coco/                     # COCO数据集
    ├── visualizations/           # 可视化文件
    ├── reports/                  # 处理报告
    └── logs/                     # 日志文件
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements_v2.txt
```

### 2. 准备数据

确保您有以下文件：
- CSV颜色映射文件（如：`airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv`）
- AirSim分割图像文件（如：`fishmask.jpg`）

### 3. 配置设置

复制配置模板并修改：
```bash
cp config_template.yaml my_config.yaml
```

编辑 `my_config.yaml` 中的关键设置：
```yaml
input:
  csv_file: "your_csv_file.csv"
  mode: "single"  # 或 "batch"
  single_image: "your_image.jpg"

detection:
  target_keywords: ["yu", "diao", "shark"]  # 目标关键词

output:
  base_directory: "my_output"
```

### 4. 运行处理

```bash
python airsim_coco_generator.py --config my_config.yaml
```

## 📖 详细使用说明

### 配置文件详解

#### 基本设置
```yaml
project:
  name: "my_dataset"              # 数据集名称
  description: "数据集描述"        # 数据集描述
  version: "1.0"                  # 版本号
```

#### 输入设置
```yaml
input:
  csv_file: "colormap.csv"        # CSV文件路径
  mode: "single"                  # single: 单张图像, batch: 批量处理
  single_image: "image.jpg"       # 单张图像路径
  batch_directory: "images/"      # 批量处理目录
```

#### 检测设置
```yaml
detection:
  target_keywords:                # 目标关键词列表
    - "yu"                        # 鱼类
    - "diao"                      # 雕类
    - "shark"                     # 鲨鱼
  min_area: 100                   # 最小面积阈值
  show_unmatched_objects: true    # 显示未匹配对象
```

#### 输出设置
```yaml
output:
  base_directory: "output"        # 输出根目录
  visualization:
    generate: true                # 生成可视化
    show_bbox: true              # 显示边界框
    show_segmentation: true      # 显示分割掩码
  reports:
    generate: true               # 生成报告
```

### 处理模式

#### 单张图像处理
```yaml
input:
  mode: "single"
  single_image: "fishmask.jpg"
```

#### 批量处理
```yaml
input:
  mode: "batch"
  batch_directory: "images/"
  supported_formats: [".jpg", ".png", ".bmp"]
```

### 目标检测配置

#### 鱼类检测
```yaml
detection:
  target_keywords: ["yu", "diao", "shark", "shayu"]
  name_mapping:
    tongshiguiyu: "铜师鬼鱼"
    shayu: "鲨鱼"
```

#### 环境对象检测
```yaml
detection:
  target_keywords: ["boulder", "ground", "water"]
  name_mapping:
    boulder: "巨石"
    ground: "地面"
    water: "水体"
```

## 🎨 输出文件说明

### COCO数据集 (`coco/dataset.json`)
标准COCO格式的数据集文件，包含：
- 图像信息
- 标注信息（边界框、分割掩码）
- 类别信息

### 可视化文件 (`visualizations/`)
- 原始分割图像
- 检测结果叠加
- 边界框可视化
- 统计图表

### 处理报告 (`reports/processing_report.json`)
详细的处理统计信息：
- 数据集摘要
- 处理统计
- 详细结果

### 日志文件 (`logs/processing.log`)
完整的处理日志记录

## 🔧 高级功能

### 数据集划分
```yaml
dataset_split:
  enable: true
  train_ratio: 0.7
  val_ratio: 0.2
  test_ratio: 0.1
```

### 中文支持
```yaml
chinese_support:
  enable: true
  font_family: ["SimHei", "Microsoft YaHei"]
```

### 性能调优
```yaml
performance:
  max_workers: 4
  memory_limit_mb: 2048
```

## 📊 使用示例

### 示例1：检测鱼类
```bash
# 使用默认配置检测鱼类
python airsim_coco_generator.py --config config_template.yaml
```

### 示例2：检测环境对象
修改配置文件中的目标关键词：
```yaml
detection:
  target_keywords: ["boulder", "ground", "water", "floor"]
```

### 示例3：批量处理
```yaml
input:
  mode: "batch"
  batch_directory: "my_images/"
dataset_split:
  enable: true
```

## 🎯 运行示例脚本

```bash
# 运行所有示例
python example_usage_v2.py
```

这将演示：
- 单张图像处理
- 批量处理
- 自定义关键词检测

## 🔍 故障排除

### 常见问题

1. **中文字体显示问题**
   - 安装中文字体：`SimHei`, `Microsoft YaHei`
   - 或在配置中禁用中文支持

2. **内存不足**
   - 减少 `performance.max_workers`
   - 降低图像分辨率

3. **没有检测到目标**
   - 检查 `target_keywords` 设置
   - 确认图像中包含目标对象
   - 查看处理报告中的详细分析

### 调试模式
```yaml
debug:
  save_intermediate_files: true
  verbose_output: true
  color_analysis: true
```

## 📈 性能特点

- **处理速度**：单张图像 < 1秒
- **内存使用**：< 2GB（默认设置）
- **支持格式**：JPG, PNG, BMP, TIFF
- **最大图像**：无限制（受内存限制）

## 🔍 数据集验证

### 验证工具套件

本项目包含完整的COCO数据集验证工具，确保生成的数据集完全符合实例分割标准：

```bash
# 完整验证（推荐）
python validate.py --coco output/coco/dataset.json --images . --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv

# 快速验证（跳过掩码验证）
python validate.py --coco output/coco/dataset.json --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv --skip-mask

# 仅COCO格式验证
python validate.py --coco output/coco/dataset.json --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv --coco-only
```

### 验证功能

- ✅ **COCO格式规范验证** - 严格验证JSON格式符合官方标准
- ✅ **实例分割特异性验证** - 确保每个CSV实例独立转换（防止实例合并）
- ✅ **几何精度验证** - 验证bbox和segmentation的准确性
- ✅ **二值掩码验证** - 像素级精度对比验证
- ✅ **可视化生成** - 生成标注可视化和对比图像
- ✅ **性能监控** - 监控验证过程的资源使用

### 验证结果

验证完成后会生成详细报告：
- `validation_tools/validation_results/` - 验证结果目录
- `validation_summary.html` - 总结报告（推荐查看）
- `visualizations/` - 标注可视化图像
- `mask_validation/` - 掩码对比图像

详细使用说明请参考 `validation_tools/README.md` 和 `validation_tools/VALIDATION_GUIDE.md`。

## 🔄 版本更新

### v2.0 新特性
- 全新的配置文件系统
- 面向对象的代码架构
- 完整的中文支持
- 详细的统计分析
- 可视化生成
- 批量处理支持
- 数据集自动划分
- **完整的验证工具套件**

### 与v1.0的区别
- 更灵活的配置方式
- 更好的错误处理
- 更详细的输出信息
- 更强的扩展性
- **实例分割问题修复**
- **全面的数据质量验证**

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**🎉 现在您可以轻松地将AirSim语义分割数据转换为COCO格式，用于深度学习模型训练！**
