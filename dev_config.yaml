# AirSim语义分割数据处理配置文件
# 配置文件版本: 1.0

# 基本设置
project:
  name: "airsim_marine_dataset"           # 数据集名称
  description: "AirSim海洋仿真语义分割数据集"  # 数据集描述
  version: "1.0"                          # 版本号
  author: "AirSim Data Processor"         # 作者

# 输入设置
input:
  csv_file: "airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv"  # CSV颜色映射文件路径
  
  # 图像输入模式：'single' 或 'batch'
  mode: "single"                          # single: 单张图片, batch: 批量处理
  
  # 单张图片模式设置
  single_image: "testmask.jpg"            # 单张图片路径
  
  # 批量处理模式设置
  batch_directory: "images/"              # 图片目录路径
  supported_formats: [".jpg", ".jpeg", ".png", ".bmp", ".tiff"]  # 支持的图像格式

# 目标检测设置
detection:
  # 目标类别关键词列表（用于从CSV中筛选目标物体）
  target_keywords:
    - "yu"          # 鱼类
    - "diao"        # 雕类
    - "shark"       # 鲨鱼
    - "shayu"       # 鲨鱼（中文拼音）
    - "feiji"       # 飞鸡鱼
    - "longye"      # 龙叶
    - "douyu"       # 斗鱼
    - "wfliedybw"  #模型变体
  # 最小物体面积阈值（像素数量，小于此值的物体将被过滤）
  min_area: 100
  
  # 是否显示未匹配关键词的物体统计
  show_unmatched_objects: true
  
  # 物体名称映射（可选，用于自定义显示名称）
  name_mapping:
    tongshiguiyu: "铜师鬼鱼"
    shitoujinyu: "石头金鱼"
    hongdidiao: "红底雕"
    niluokoufeiji: "尼罗口飞鸡"
    chixuehonglongye: "赤血红龙叶"
    yinnihuyu: "印尼虎鱼"
    shuangdaigongziaohei: "双带公雕黑"
    shiwangdouyu: "狮王斗鱼"
    shayu: "鲨鱼"
    wfliedybw: "模型变体"

# 输出设置
output:
  base_directory: "output"                # 输出根目录
  
  # 子目录结构
  subdirectories:
    coco: "coco"                         # COCO数据集目录
    visualizations: "visualizations"     # 可视化文件目录
    logs: "logs"                         # 日志文件目录
    reports: "reports"                   # 报告文件目录
  
  # COCO数据集设置
  coco:
    generate: true                       # 是否生成COCO数据集
    filename: "dataset.json"             # COCO文件名
    include_segmentation: true           # 是否包含分割信息
    include_bbox: true                   # 是否包含边界框信息
  
  # 可视化设置
  visualization:
    generate: false                       # 是否生成可视化文件
    show_bbox: true                      # 是否显示边界框
    show_segmentation: true              # 是否显示分割掩码
    show_labels: true                    # 是否显示标签
    show_statistics: true                # 是否显示统计图表
    dpi: 300                            # 图像分辨率
    font_size: 12                       # 字体大小
  
  # 报告设置
  reports:
    generate: true                       # 是否生成处理报告
    include_statistics: true             # 是否包含统计信息
    include_color_mapping: true          # 是否包含颜色映射信息
    format: "json"                       # 报告格式：json 或 txt

# 数据集划分设置
dataset_split:
  enable: false                          # 是否启用数据集划分
  train_ratio: 0.7                      # 训练集比例
  val_ratio: 0.2                        # 验证集比例
  test_ratio: 0.1                       # 测试集比例
  random_seed: 42                       # 随机种子（确保结果可重现）

# 日志设置
logging:
  level: "INFO"                          # 日志级别：DEBUG, INFO, WARNING, ERROR
  console_output: true                   # 是否输出到控制台
  file_output: true                      # 是否输出到文件
  log_filename: "processing.log"         # 日志文件名
  
# 性能设置
performance:
  max_workers: 4                         # 并行处理的最大工作线程数
  memory_limit_mb: 2048                  # 内存使用限制（MB）
  
# 中文支持设置
chinese_support:
  enable: true                           # 是否启用中文支持
  font_family: ["SimHei", "Microsoft YaHei", "DejaVu Sans", "Arial Unicode MS"]  # 中文字体列表
  encoding: "utf-8"                      # 文件编码

# 调试设置
debug:
  save_intermediate_files: false         # 是否保存中间处理文件
  verbose_output: false                  # 是否输出详细信息
  color_analysis: false                  # 是否进行详细的颜色分析
