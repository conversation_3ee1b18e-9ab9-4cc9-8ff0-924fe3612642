# AirSim COCO数据集生成器依赖包
# 版本: 2.0

# 核心数据处理
numpy>=1.21.0
pandas>=1.3.0
opencv-python>=4.5.0

# 配置文件处理
PyYAML>=6.0

# 图像处理和可视化
matplotlib>=3.4.0
Pillow>=8.3.0

# 进度条和用户界面
tqdm>=4.62.0

# 类型提示支持
typing-extensions>=4.0.0

# 可选：深度学习框架（如果需要与MMDetection集成）
# torch>=1.9.0
# torchvision>=0.10.0
# mmdet>=2.25.0
# mmcv-full>=1.4.0

# 可选：更高级的图像处理
# scikit-image>=0.18.0
# scipy>=1.7.0

# 可选：数据可视化增强
# seaborn>=0.11.0
# plotly>=5.0.0

# 可选：并行处理
# joblib>=1.0.0

# 开发和测试工具（可选）
# pytest>=6.0.0
# pytest-cov>=2.12.0
# black>=21.0.0
# flake8>=3.9.0
