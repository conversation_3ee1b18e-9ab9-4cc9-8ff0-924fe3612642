# AirSim海洋数据处理器使用指南

## 概述

AirSim海洋数据处理器是一个专门用于处理AirSim海洋仿真环境语义分割数据的Python工具。它能够自动识别和提取鱼类对象，生成可视化掩码，并将数据转换为COCO格式，便于深度学习模型训练。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 核心功能

### 1. 鱼类数据提取
- 自动从CSV颜色映射表中识别鱼类对象
- 基于中文拼音关键词进行智能筛选
- 保持鱼类子类别的独立性

### 2. 掩码生成
- 为每个鱼类子类别生成独立的二值掩码
- 创建彩色可视化掩码便于人工检查
- 支持多实例分割

### 3. COCO数据集转换
- 生成符合COCO标准的JSON标注文件
- 包含边界框和分割掩码信息
- 与MMDetection等框架完全兼容

### 4. 数据集划分
- 支持训练/验证/测试集自动划分
- 可自定义划分比例
- 保持数据分布的一致性

## 使用方法

### 命令行接口

#### 基本用法

```bash
# 处理单张图像
python airsim_marine_data_processor.py \
    --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv \
    --image path/to/segmentation_image.png \
    --output output_directory

# 批量处理图像目录
python airsim_marine_data_processor.py \
    --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv \
    --image_dir path/to/segmentation_images/ \
    --output output_directory
```

#### 高级用法

```bash
# 包含数据集划分
python airsim_marine_data_processor.py \
    --csv airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv \
    --image_dir path/to/images/ \
    --output output_directory \
    --split \
    --train_ratio 0.7 \
    --val_ratio 0.2 \
    --test_ratio 0.1 \
    --dataset_name my_marine_dataset
```

### 参数说明

| 参数 | 必需 | 说明 | 默认值 |
|------|------|------|--------|
| `--csv` | 是 | 颜色映射CSV文件路径 | - |
| `--image` | 否* | 单张分割图像路径 | - |
| `--image_dir` | 否* | 分割图像目录路径 | - |
| `--output` | 否 | 输出目录 | "output" |
| `--dataset_name` | 否 | 数据集名称 | "airsim_marine" |
| `--split` | 否 | 是否划分数据集 | False |
| `--train_ratio` | 否 | 训练集比例 | 0.7 |
| `--val_ratio` | 否 | 验证集比例 | 0.2 |
| `--test_ratio` | 否 | 测试集比例 | 0.1 |

*注：`--image` 和 `--image_dir` 必须指定其中一个

### Python API使用

```python
from airsim_marine_data_processor import AirSimMarineDataProcessor

# 初始化处理器
processor = AirSimMarineDataProcessor(
    csv_path="airsim_segmentation_colormap_list_2025_07_29_10_50_24.csv",
    output_dir="output"
)

# 保存鱼类类别信息
processor.save_fish_categories()

# 处理单张图像
result = processor.process_segmentation_image("image.png", image_id=1)

# 创建可视化
processor.create_visualization("image.png", result)

# 创建COCO数据集
coco_file = processor.create_coco_dataset([result], "my_dataset")

# 划分数据集
processor.split_dataset(str(coco_file))
```

## 输出结构

```
output/
├── fish_categories.json          # 鱼类类别信息
├── masks/                        # 二值掩码文件
│   ├── mask_1_tongshiguiyu.png
│   ├── mask_1_shitoujinyu.png
│   └── ...
├── visualizations/               # 可视化结果
│   ├── visualization_1.png
│   └── ...
└── coco/                        # COCO格式数据集
    ├── airsim_marine.json       # 完整数据集
    ├── airsim_marine_train.json # 训练集
    ├── airsim_marine_val.json   # 验证集
    ├── airsim_marine_test.json  # 测试集
    └── airsim_marine_report.json # 数据集报告
```

## 支持的鱼类类别

处理器会自动识别以下鱼类（基于对象名称）：

| 类别ID | 中文名称 | 英文标识 | RGB颜色 |
|--------|----------|----------|---------|
| 1 | 铜师鬼鱼 | tongshiguiyu | (159, 255, 127) |
| 2 | 石头金鱼 | shitoujinyu | (159, 255, 191) |
| 3 | 红底雕 | hongdidiao | (159, 255, 95) |
| 4 | 尼罗口飞鸡 | niluokoufeiji | (159, 127, 255) |
| 5 | 赤血红龙叶 | chixuehonglongye | (159, 127, 127) |
| 6 | 印尼虎鱼 | yinnihuyu | (159, 127, 191) |
| 7 | 双带公雕黑 | shuangdaigongziaohei | (159, 127, 95) |
| 8 | 狮王斗鱼 | shiwangdouyu | (159, 191, 255) |
| 9 | 鲨鱼 | shayu | (159, 191, 127) |

## 运行示例

```bash
# 运行内置示例
python example_usage.py
```

这将创建示例分割图像并演示完整的处理流程。

## 与MMDetection集成

生成的COCO格式数据集可以直接用于MMDetection训练：

```python
# MMDetection配置示例
dataset_type = 'CocoDataset'
classes = ('tongshiguiyu', 'shitoujinyu', 'hongdidiao', 'shayu', ...)

data = dict(
    train=dict(
        type=dataset_type,
        ann_file='output/coco/airsim_marine_train.json',
        img_prefix='path/to/images/',
        classes=classes),
    val=dict(
        type=dataset_type,
        ann_file='output/coco/airsim_marine_val.json',
        img_prefix='path/to/images/',
        classes=classes),
    test=dict(
        type=dataset_type,
        ann_file='output/coco/airsim_marine_test.json',
        img_prefix='path/to/images/',
        classes=classes)
)
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'cv2'**
   ```bash
   pip install opencv-python
   ```

2. **无法找到鱼类对象**
   - 检查CSV文件格式是否正确
   - 确认图像中包含对应的RGB颜色值

3. **内存不足**
   - 减少批处理的图像数量
   - 降低图像分辨率

4. **COCO格式验证失败**
   - 检查生成的JSON文件格式
   - 确认所有必需字段都存在

### 性能优化

- 使用SSD存储提高I/O性能
- 对于大批量处理，考虑使用多进程
- 适当调整图像分辨率以平衡精度和速度

## 扩展功能

### 添加新的鱼类类别

1. 在CSV文件中添加新的对象映射
2. 更新 `fish_keywords` 列表
3. 添加对应的显示名称映射

### 自定义可视化

修改 `create_visualization` 方法以自定义输出格式和样式。

### 集成其他数据格式

可以扩展处理器以支持YOLO、Pascal VOC等其他标注格式。

## 许可证

本项目遵循MIT许可证。
