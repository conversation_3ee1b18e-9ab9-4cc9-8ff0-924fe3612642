#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AirSim语义分割数据COCO格式转换器

基于CSV颜色映射表和分割图像生成COCO数据集的完整解决方案

作者: AI Assistant
版本: 2.0
日期: 2025-07-29
"""

import os
import sys
import json
import yaml
import logging
import argparse
import cv2
import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from tqdm import tqdm
import warnings

# 设置中文字体支持
def setup_chinese_fonts():
    """设置中文字体支持"""
    import platform
    import matplotlib.font_manager as fm

    # 根据操作系统选择合适的中文字体
    system = platform.system()

    if system == "Darwin":  # macOS
        chinese_fonts = [
            'Arial Unicode MS',      # 支持中文的Arial (优先)
            'PingFang SC',           # macOS 默认中文字体
            'Hiragino Sans GB',      # macOS 中文字体
            'STHeiti',               # 华文黑体
            'SimHei',                # 黑体
            'Microsoft YaHei'        # 微软雅黑
        ]
    elif system == "Windows":  # Windows
        chinese_fonts = [
            'Microsoft YaHei',       # 微软雅黑
            'SimHei',                # 黑体
            'SimSun',                # 宋体
            'KaiTi',                 # 楷体
            'Arial Unicode MS'       # 支持中文的Arial
        ]
    else:  # Linux
        chinese_fonts = [
            'WenQuanYi Micro Hei',   # 文泉驿微米黑
            'WenQuanYi Zen Hei',     # 文泉驿正黑
            'Noto Sans CJK SC',      # Google Noto字体
            'Source Han Sans SC',     # 思源黑体
            'DejaVu Sans'            # 备选字体
        ]

    # 检查可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # 找到第一个可用的中文字体
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break

    # 如果没有找到中文字体，使用系统默认字体
    if selected_font is None:
        print("⚠️ 警告: 未找到合适的中文字体，可能会出现中文显示问题")
        selected_font = 'DejaVu Sans'
    else:
        print(f"✅ 使用中文字体: {selected_font}")

    # 设置matplotlib字体
    plt.rcParams['font.sans-serif'] = [selected_font] + chinese_fonts + ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    return selected_font

# 初始化中文字体
setup_chinese_fonts()

# 忽略matplotlib字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')


class ConfigManager:
    """配置文件管理器"""
    
    def __init__(self, config_path: str):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.endswith('.yaml') or self.config_path.endswith('.yml'):
                    return yaml.safe_load(f)
                elif self.config_path.endswith('.json'):
                    return json.load(f)
                else:
                    raise ValueError("配置文件必须是YAML或JSON格式")
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        except Exception as e:
            raise Exception(f"加载配置文件失败: {e}")
    
    def _validate_config(self):
        """验证配置文件的完整性"""
        required_sections = ['project', 'input', 'detection', 'output']
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必需的部分: {section}")
    
    def get(self, key_path: str, default=None):
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 'input.csv_file'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value


class Logger:
    """日志管理器"""
    
    def __init__(self, config: ConfigManager, output_dir: Path):
        """
        初始化日志管理器
        
        Args:
            config: 配置管理器
            output_dir: 输出目录
        """
        self.config = config
        self.output_dir = output_dir
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('AirSimCOCOGenerator')
        logger.setLevel(getattr(logging, self.config.get('logging.level', 'INFO')))
        
        # 清除现有的处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台输出
        if self.config.get('logging.console_output', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件输出
        if self.config.get('logging.file_output', True):
            log_dir = self.output_dir / self.config.get('output.subdirectories.logs', 'logs')
            log_dir.mkdir(parents=True, exist_ok=True)
            
            log_file = log_dir / self.config.get('logging.log_filename', 'processing.log')
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)


class ColorMappingAnalyzer:
    """颜色映射分析器"""
    
    def __init__(self, csv_path: str, config: ConfigManager, logger: Logger):
        """
        初始化颜色映射分析器
        
        Args:
            csv_path: CSV文件路径
            config: 配置管理器
            logger: 日志管理器
        """
        self.csv_path = csv_path
        self.config = config
        self.logger = logger
        self.color_mapping = self._load_color_mapping()
        self.target_categories = self._extract_target_categories()
    
    def _load_color_mapping(self) -> pd.DataFrame:
        """加载颜色映射表"""
        try:
            df = pd.read_csv(self.csv_path, encoding='utf-8')
            self.logger.info(f"成功加载颜色映射表: {len(df)} 个对象")
            return df
        except Exception as e:
            self.logger.error(f"加载CSV文件失败: {e}")
            raise
    
    def _extract_target_categories(self) -> Dict[str, Dict]:
        """提取目标类别和实例信息（实例分割模式）"""
        target_keywords = self.config.get('detection.target_keywords', [])
        name_mapping = self.config.get('detection.name_mapping', {})

        categories = {}  # 类别信息
        instances = {}   # 实例信息
        category_id = 1
        instance_id = 1

        for _, row in self.color_mapping.iterrows():
            object_name = row['ObjectName'].lower()

            # 检查是否匹配目标关键词
            matched_keyword = None
            for keyword in target_keywords:
                if keyword in object_name:
                    matched_keyword = keyword
                    break

            if matched_keyword:
                # 提取基础类别名称（用于COCO类别）
                base_name = object_name.split('_')[0]

                # 如果是新类别，创建类别信息
                if base_name not in categories:
                    display_name = name_mapping.get(base_name, base_name)
                    categories[base_name] = {
                        'id': category_id,
                        'name': base_name,
                        'display_name': display_name,
                        'keyword': matched_keyword,
                        'instance_count': 0
                    }
                    category_id += 1

                # 为每个实例创建独立记录
                instance_color = (int(row['R']), int(row['G']), int(row['B']))
                instances[instance_color] = {
                    'instance_id': instance_id,
                    'object_name': row['ObjectName'],
                    'category_id': categories[base_name]['id'],
                    'category_name': base_name,
                    'display_name': categories[base_name]['display_name'],
                    'color': instance_color
                }

                categories[base_name]['instance_count'] += 1
                instance_id += 1

        # 保存实例信息到类中
        self.instances = instances

        self.logger.info(f"提取到 {len(categories)} 个目标类别，{len(instances)} 个实例")
        return categories
    
    def get_color_to_instance_mapping(self) -> Dict[Tuple[int, int, int], Dict]:
        """获取颜色到实例的映射（实例分割模式）"""
        return self.instances

    def get_color_to_category_mapping(self) -> Dict[Tuple[int, int, int], Dict]:
        """获取颜色到类别的映射（兼容性方法）"""
        # 为了向后兼容，返回实例映射
        return self.instances
    
    def analyze_image_colors(self, image_path: str) -> Dict[str, Any]:
        """分析图像中的颜色分布"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            unique_colors = np.unique(image_rgb.reshape(-1, 3), axis=0)
            
            analysis = {
                'image_path': image_path,
                'image_size': image.shape,
                'unique_colors_count': len(unique_colors),
                'unique_colors': [tuple(color) for color in unique_colors],
                'matched_objects': {},
                'unmatched_colors': [],
                'target_categories_found': {}
            }
            
            # 分析匹配的对象
            color_to_category = self.get_color_to_category_mapping()
            
            for color in unique_colors:
                color_tuple = tuple(color)
                
                if color_tuple in color_to_category:
                    category_info = color_to_category[color_tuple]
                    category_name = category_info['category_name']
                    
                    if category_name not in analysis['target_categories_found']:
                        analysis['target_categories_found'][category_name] = {
                            'category_id': category_info['category_id'],
                            'display_name': category_info['display_name'],
                            'objects': []
                        }
                    
                    # 计算该颜色的像素数量
                    color_mask = np.all(image_rgb == color, axis=2)
                    pixel_count = np.sum(color_mask)
                    
                    analysis['matched_objects'][color_tuple] = {
                        'object_name': category_info['object_name'],
                        'category_name': category_name,
                        'display_name': category_info['display_name'],
                        'pixel_count': int(pixel_count),
                        'percentage': float(pixel_count / (image.shape[0] * image.shape[1]) * 100)
                    }
                    
                    analysis['target_categories_found'][category_name]['objects'].append({
                        'color': color_tuple,
                        'object_name': category_info['object_name'],
                        'pixel_count': int(pixel_count)
                    })
                else:
                    # 检查是否在CSV中但不是目标类别
                    csv_match = self.color_mapping[
                        (self.color_mapping['R'] == color[0]) &
                        (self.color_mapping['G'] == color[1]) &
                        (self.color_mapping['B'] == color[2])
                    ]
                    
                    if not csv_match.empty:
                        object_name = csv_match.iloc[0]['ObjectName']
                        color_mask = np.all(image_rgb == color, axis=2)
                        pixel_count = np.sum(color_mask)
                        
                        analysis['unmatched_colors'].append({
                            'color': color_tuple,
                            'object_name': object_name,
                            'pixel_count': int(pixel_count),
                            'percentage': float(pixel_count / (image.shape[0] * image.shape[1]) * 100)
                        })
            
            return analysis

        except Exception as e:
            self.logger.error(f"分析图像颜色失败: {e}")
            raise


class COCODatasetGenerator:
    """COCO数据集生成器"""

    def __init__(self, config: ConfigManager, logger: Logger, color_analyzer: ColorMappingAnalyzer):
        """
        初始化COCO数据集生成器

        Args:
            config: 配置管理器
            logger: 日志管理器
            color_analyzer: 颜色映射分析器
        """
        self.config = config
        self.logger = logger
        self.color_analyzer = color_analyzer
        self.annotation_id = 1

    def process_single_image(self, image_path: str, image_id: int) -> Dict[str, Any]:
        """
        处理单张图像

        Args:
            image_path: 图像路径
            image_id: 图像ID

        Returns:
            处理结果字典
        """
        try:
            self.logger.info(f"处理图像: {image_path}")

            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")

            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            height, width = image_rgb.shape[:2]

            # 创建结果字典
            result = {
                'image_info': {
                    'id': image_id,
                    'file_name': os.path.basename(image_path),
                    'width': width,
                    'height': height,
                    'date_captured': datetime.now().isoformat()
                },
                'annotations': [],
                'statistics': {
                    'total_objects': 0,
                    'categories_found': {},
                    'processing_time': 0
                }
            }

            start_time = datetime.now()

            # 获取颜色到实例的映射
            color_to_instance = self.color_analyzer.get_color_to_instance_mapping()
            min_area = self.config.get('detection.min_area', 100)

            # 处理每个实例（实例分割模式）
            for color, instance_info in color_to_instance.items():
                # 创建该实例的颜色掩码
                color_mask = np.all(image_rgb == color, axis=2)

                if not np.any(color_mask):
                    continue  # 该实例在当前图像中不存在

                # 转换为uint8掩码
                instance_mask = color_mask.astype(np.uint8) * 255

                # 查找轮廓
                contours, _ = cv2.findContours(instance_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    area = cv2.contourArea(contour)

                    if area < min_area:
                        continue

                    # 计算边界框
                    x, y, w, h = cv2.boundingRect(contour)

                    # 创建分割掩码
                    segmentation = self._contour_to_segmentation(contour)

                    if not segmentation:
                        continue

                    # 创建COCO格式的标注（每个实例一个annotation）
                    annotation = {
                        'id': self.annotation_id,
                        'image_id': image_id,
                        'category_id': instance_info['category_id'],
                        'bbox': [int(x), int(y), int(w), int(h)],
                        'area': float(area),
                        'segmentation': segmentation,
                        'iscrowd': 0,
                        # 添加实例信息用于调试
                        'instance_name': instance_info['object_name'],
                        'instance_color': color
                    }

                    result['annotations'].append(annotation)
                    self.annotation_id += 1

                    # 更新统计信息
                    category_name = instance_info['category_name']
                    if category_name not in result['statistics']['categories_found']:
                        result['statistics']['categories_found'][category_name] = 0
                    result['statistics']['categories_found'][category_name] += 1



            # 更新统计信息
            result['statistics']['total_objects'] = len(result['annotations'])
            result['statistics']['processing_time'] = (datetime.now() - start_time).total_seconds()

            self.logger.info(f"图像处理完成: 检测到 {result['statistics']['total_objects']} 个对象")
            return result

        except Exception as e:
            self.logger.error(f"处理图像失败: {e}")
            raise

    def _contour_to_segmentation(self, contour) -> List[List[float]]:
        """将轮廓转换为COCO分割格式"""
        try:
            # 简化轮廓
            epsilon = 0.005 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # 转换为平坦列表
            segmentation = approx.flatten().tolist()

            # 确保至少有6个点（3个坐标对）
            if len(segmentation) < 6:
                return []

            return [segmentation]

        except Exception as e:
            self.logger.warning(f"轮廓转换失败: {e}")
            return []

    def create_coco_dataset(self, image_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        创建完整的COCO数据集

        Args:
            image_results: 图像处理结果列表

        Returns:
            COCO格式数据集
        """
        try:
            self.logger.info("创建COCO数据集...")

            # 创建COCO格式数据
            coco_data = {
                "info": {
                    "description": self.config.get('project.description', 'AirSim Semantic Segmentation Dataset'),
                    "url": "",
                    "version": self.config.get('project.version', '1.0'),
                    "year": datetime.now().year,
                    "contributor": self.config.get('project.author', 'AirSim COCO Generator'),
                    "date_created": datetime.now().isoformat()
                },
                "licenses": [
                    {
                        "id": 1,
                        "name": "Unknown",
                        "url": ""
                    }
                ],
                "images": [],
                "annotations": [],
                "categories": []
            }

            # 添加类别信息
            for category_name, category_info in self.color_analyzer.target_categories.items():
                coco_data["categories"].append({
                    "id": category_info['id'],
                    "name": category_name,
                    "supercategory": "object"
                })

            # 添加图像和标注信息
            for result in image_results:
                coco_data["images"].append(result['image_info'])
                coco_data["annotations"].extend(result['annotations'])

            self.logger.info(f"COCO数据集创建完成: {len(coco_data['images'])} 张图像, {len(coco_data['annotations'])} 个标注")
            return coco_data

        except Exception as e:
            self.logger.error(f"创建COCO数据集失败: {e}")
            raise


class VisualizationGenerator:
    """可视化生成器"""

    def __init__(self, config: ConfigManager, logger: Logger, color_analyzer: ColorMappingAnalyzer):
        """
        初始化可视化生成器

        Args:
            config: 配置管理器
            logger: 日志管理器
            color_analyzer: 颜色映射分析器
        """
        self.config = config
        self.logger = logger
        self.color_analyzer = color_analyzer

    def create_visualization(self, image_path: str, result: Dict[str, Any],
                           color_analysis: Dict[str, Any], output_path: str):
        """
        创建可视化图像

        Args:
            image_path: 原始图像路径
            result: 处理结果
            color_analysis: 颜色分析结果
            output_path: 输出路径
        """
        try:
            self.logger.info(f"创建可视化图像: {output_path}")

            # 读取原始图像
            image = cv2.imread(image_path)
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 创建图像布局
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'AirSim语义分割检测结果 - {result["image_info"]["file_name"]}',
                        fontsize=16, fontweight='bold')

            # 1. 原始图像
            axes[0, 0].imshow(image_rgb)
            axes[0, 0].set_title('原始分割图像', fontsize=14)
            axes[0, 0].axis('off')

            # 2. 检测结果叠加
            overlay_image = self._create_overlay_image(image_rgb, result, color_analysis)
            axes[0, 1].imshow(overlay_image)
            axes[0, 1].set_title('目标检测叠加', fontsize=14)
            axes[0, 1].axis('off')

            # 3. 边界框可视化
            bbox_image = self._create_bbox_image(image_rgb, result)
            axes[1, 0].imshow(bbox_image)
            axes[1, 0].set_title('边界框检测结果', fontsize=14)
            axes[1, 0].axis('off')

            # 4. 统计图表
            self._create_statistics_chart(axes[1, 1], result, color_analysis)

            plt.tight_layout()

            # 保存图像
            dpi = self.config.get('output.visualization.dpi', 300)
            plt.savefig(output_path, dpi=dpi, bbox_inches='tight', facecolor='white')
            plt.close()

            self.logger.info(f"可视化图像已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化失败: {e}")
            raise

    def _create_overlay_image(self, image: np.ndarray, result: Dict[str, Any],
                             color_analysis: Dict[str, Any]) -> np.ndarray:
        """创建叠加图像"""
        overlay = image.copy()

        # 为每个检测到的类别创建半透明叠加
        for category_name, category_data in color_analysis['target_categories_found'].items():
            category_info = self.color_analyzer.target_categories[category_name]

            # 使用类别的主要颜色
            color = np.array(category_info['color'])

            # 创建该类别的掩码
            category_mask = np.zeros(image.shape[:2], dtype=np.uint8)
            for obj in category_data['objects']:
                obj_color = obj['color']
                color_mask = np.all(image == obj_color, axis=2)
                category_mask[color_mask] = 255

            # 创建彩色叠加
            colored_mask = np.zeros_like(image)
            colored_mask[category_mask > 0] = color

            # 半透明叠加
            alpha = 0.4
            overlay = cv2.addWeighted(overlay, 1-alpha, colored_mask, alpha, 0)

        return overlay

    def _create_bbox_image(self, image: np.ndarray, result: Dict[str, Any]) -> np.ndarray:
        """创建边界框图像"""
        bbox_image = image.copy()

        for annotation in result['annotations']:
            category_id = annotation['category_id']
            bbox = annotation['bbox']

            # 找到对应的类别信息
            category_info = None
            for info in self.color_analyzer.target_categories.values():
                if info['id'] == category_id:
                    category_info = info
                    break

            if category_info:
                # 绘制边界框
                color = category_info['color']
                x, y, w, h = bbox
                cv2.rectangle(bbox_image, (x, y), (x + w, y + h), color, 2)

                # 添加标签
                label = category_info['display_name']
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(bbox_image, (x, y - label_size[1] - 10),
                             (x + label_size[0], y), color, -1)
                cv2.putText(bbox_image, label, (x, y - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return bbox_image

    def _create_statistics_chart(self, ax, result: Dict[str, Any], color_analysis: Dict[str, Any]):
        """创建统计图表"""
        categories_found = result['statistics']['categories_found']

        if not categories_found:
            ax.text(0.5, 0.5, '未检测到目标对象', ha='center', va='center',
                   transform=ax.transAxes, fontsize=14)
            ax.set_title('检测统计', fontsize=14)
            return

        # 准备数据
        names = []
        counts = []
        colors = []

        for category_name, stats in categories_found.items():
            names.append(stats['display_name'])
            counts.append(stats['count'])

            # 获取类别颜色
            category_info = self.color_analyzer.target_categories[category_name]
            color = [c/255.0 for c in category_info['color']]
            colors.append(color)

        # 创建柱状图
        bars = ax.bar(names, counts, color=colors)
        ax.set_title('目标检测统计', fontsize=14)
        ax.set_ylabel('检测数量', fontsize=12)

        # 添加数值标签
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   f'{count}', ha='center', va='bottom', fontsize=10)

        # 旋转x轴标签
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')


class ReportGenerator:
    """报告生成器"""

    def __init__(self, config: ConfigManager, logger: Logger):
        """
        初始化报告生成器

        Args:
            config: 配置管理器
            logger: 日志管理器
        """
        self.config = config
        self.logger = logger

    def generate_processing_report(self, image_results: List[Dict[str, Any]],
                                 color_analyses: List[Dict[str, Any]],
                                 coco_data: Dict[str, Any], output_path: str):
        """
        生成处理报告

        Args:
            image_results: 图像处理结果列表
            color_analyses: 颜色分析结果列表
            coco_data: COCO数据集
            output_path: 输出路径
        """
        try:
            self.logger.info(f"生成处理报告: {output_path}")

            report = {
                "report_info": {
                    "generated_at": datetime.now().isoformat(),
                    "generator_version": "2.0",
                    "config_file": self.config.config_path
                },
                "dataset_summary": {
                    "total_images": len(image_results),
                    "total_annotations": len(coco_data["annotations"]),
                    "total_categories": len(coco_data["categories"]),
                    "categories": []
                },
                "processing_statistics": {
                    "total_processing_time": 0,
                    "average_processing_time": 0,
                    "images_with_detections": 0,
                    "images_without_detections": 0
                },
                "detailed_results": []
            }

            # 统计处理时间
            total_time = sum(result['statistics']['processing_time'] for result in image_results)
            report["processing_statistics"]["total_processing_time"] = total_time
            report["processing_statistics"]["average_processing_time"] = total_time / len(image_results) if image_results else 0

            # 统计检测结果
            for result in image_results:
                if result['statistics']['total_objects'] > 0:
                    report["processing_statistics"]["images_with_detections"] += 1
                else:
                    report["processing_statistics"]["images_without_detections"] += 1

            # 类别统计
            category_stats = defaultdict(int)
            for annotation in coco_data["annotations"]:
                category_stats[annotation["category_id"]] += 1

            for category in coco_data["categories"]:
                report["dataset_summary"]["categories"].append({
                    "id": category["id"],
                    "name": category["name"],
                    "instance_count": category_stats[category["id"]]
                })

            # 详细结果
            for i, (result, analysis) in enumerate(zip(image_results, color_analyses)):
                detailed_result = {
                    "image_id": result['image_info']['id'],
                    "image_name": result['image_info']['file_name'],
                    "image_size": [result['image_info']['width'], result['image_info']['height']],
                    "processing_time": result['statistics']['processing_time'],
                    "detections": result['statistics']['total_objects'],
                    "categories_found": result['statistics']['categories_found'],
                    "color_analysis": {
                        "unique_colors": analysis['unique_colors_count'],
                        "matched_objects": len(analysis['matched_objects']),
                        "unmatched_colors": len(analysis['unmatched_colors'])
                    }
                }
                report["detailed_results"].append(detailed_result)

            # 保存报告
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            self.logger.info(f"处理报告已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            raise


class AirSimCOCOProcessor:
    """AirSim COCO数据集处理器主类"""

    def __init__(self, config_path: str):
        """
        初始化处理器

        Args:
            config_path: 配置文件路径
        """
        self.config = ConfigManager(config_path)
        self.output_dir = Path(self.config.get('output.base_directory', 'output'))
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        self.coco_dir = self.output_dir / self.config.get('output.subdirectories.coco', 'coco')
        self.viz_dir = self.output_dir / self.config.get('output.subdirectories.visualizations', 'visualizations')
        self.reports_dir = self.output_dir / self.config.get('output.subdirectories.reports', 'reports')

        for dir_path in [self.coco_dir, self.viz_dir, self.reports_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 初始化组件
        self.logger = Logger(self.config, self.output_dir)
        self.color_analyzer = ColorMappingAnalyzer(
            self.config.get('input.csv_file'), self.config, self.logger
        )
        self.coco_generator = COCODatasetGenerator(self.config, self.logger, self.color_analyzer)
        self.viz_generator = VisualizationGenerator(self.config, self.logger, self.color_analyzer)
        self.report_generator = ReportGenerator(self.config, self.logger)

        self.logger.info("AirSim COCO处理器初始化完成")

    def print_detection_statistics(self):
        """打印检测统计信息"""
        print("\n" + "="*60)
        print("🎯 AirSim语义分割数据处理器")
        print("="*60)

        # 项目信息
        print(f"📋 项目名称: {self.config.get('project.name')}")
        print(f"📝 项目描述: {self.config.get('project.description')}")
        print(f"📂 输出目录: {self.output_dir}")

        # CSV文件信息
        csv_file = self.config.get('input.csv_file')
        print(f"\n📊 CSV文件: {csv_file}")
        print(f"   总对象数: {len(self.color_analyzer.color_mapping)}")

        # 目标类别信息
        target_keywords = self.config.get('detection.target_keywords', [])
        print(f"\n🎯 目标关键词: {target_keywords}")
        print(f"   匹配的类别数: {len(self.color_analyzer.target_categories)}")

        if self.color_analyzer.target_categories:
            print("\n🐟 检测到的目标类别:")
            for category_name, category_info in self.color_analyzer.target_categories.items():
                print(f"   {category_info['id']:2d}. {category_info['display_name']:15s} "
                      f"({category_info['instance_count']} 个实例)")

            print("\n🔍 实例详细信息:")
            for color, instance_info in self.color_analyzer.instances.items():
                print(f"   - {instance_info['object_name']:20s} "
                      f"RGB{color} -> {instance_info['display_name']}")

        print("="*60)

    def analyze_images(self, image_paths: List[str]) -> List[Dict[str, Any]]:
        """
        分析图像列表

        Args:
            image_paths: 图像路径列表

        Returns:
            颜色分析结果列表
        """
        analyses = []

        print(f"\n🔍 分析 {len(image_paths)} 张图像...")

        for image_path in tqdm(image_paths, desc="分析图像"):
            try:
                analysis = self.color_analyzer.analyze_image_colors(image_path)
                analyses.append(analysis)

                # 打印分析结果
                print(f"\n📸 图像: {os.path.basename(image_path)}")
                print(f"   尺寸: {analysis['image_size'][1]}x{analysis['image_size'][0]}")
                print(f"   唯一颜色: {analysis['unique_colors_count']} 种")
                print(f"   匹配对象: {len(analysis['matched_objects'])} 个")

                if analysis['target_categories_found']:
                    print("   🎯 检测到的目标类别:")
                    for cat_name, cat_data in analysis['target_categories_found'].items():
                        print(f"      - {cat_data['display_name']}: {len(cat_data['objects'])} 个实例")
                else:
                    print("   ❌ 未检测到目标类别")

                # 显示未匹配的对象（如果配置启用）
                if (self.config.get('detection.show_unmatched_objects', False) and
                    analysis['unmatched_colors']):
                    print("   📋 其他对象:")
                    for obj in analysis['unmatched_colors'][:5]:  # 只显示前5个
                        print(f"      - {obj['object_name']}: {obj['pixel_count']} 像素")
                    if len(analysis['unmatched_colors']) > 5:
                        print(f"      - ... 还有 {len(analysis['unmatched_colors'])-5} 个对象")

            except Exception as e:
                self.logger.error(f"分析图像失败 {image_path}: {e}")
                continue

        return analyses

    def process_images(self, image_paths: List[str]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        处理图像列表

        Args:
            image_paths: 图像路径列表

        Returns:
            (处理结果列表, 颜色分析结果列表)
        """
        # 首先进行颜色分析
        color_analyses = self.analyze_images(image_paths)

        # 然后进行COCO处理
        print(f"\n🔄 处理 {len(image_paths)} 张图像生成COCO数据集...")

        image_results = []

        for i, (image_path, analysis) in enumerate(zip(image_paths, color_analyses), 1):
            try:
                result = self.coco_generator.process_single_image(image_path, i)
                image_results.append(result)

                # 生成可视化（如果启用）
                if self.config.get('output.visualization.generate', True):
                    viz_filename = f"visualization_{i}_{Path(image_path).stem}.png"
                    viz_path = self.viz_dir / viz_filename
                    self.viz_generator.create_visualization(image_path, result, analysis, str(viz_path))

            except Exception as e:
                self.logger.error(f"处理图像失败 {image_path}: {e}")
                continue

        return image_results, color_analyses

    def get_image_paths(self) -> List[str]:
        """获取要处理的图像路径列表"""
        mode = self.config.get('input.mode', 'single')

        if mode == 'single':
            image_path = self.config.get('input.single_image')
            if not image_path or not os.path.exists(image_path):
                raise FileNotFoundError(f"单张图像文件不存在: {image_path}")
            return [image_path]

        elif mode == 'batch':
            batch_dir = self.config.get('input.batch_directory')
            if not batch_dir or not os.path.exists(batch_dir):
                raise FileNotFoundError(f"批量处理目录不存在: {batch_dir}")

            supported_formats = self.config.get('input.supported_formats', ['.jpg', '.png'])
            image_paths = []

            for ext in supported_formats:
                pattern = f"*{ext}"
                image_paths.extend(Path(batch_dir).glob(pattern))
                # 也搜索大写扩展名
                pattern = f"*{ext.upper()}"
                image_paths.extend(Path(batch_dir).glob(pattern))

            if not image_paths:
                raise ValueError(f"在目录 {batch_dir} 中未找到支持的图像文件")

            return [str(path) for path in image_paths]

        else:
            raise ValueError(f"不支持的处理模式: {mode}")

    def run(self):
        """运行完整的处理流程"""
        try:
            # 打印统计信息
            self.print_detection_statistics()

            # 获取图像路径
            image_paths = self.get_image_paths()
            self.logger.info(f"找到 {len(image_paths)} 张图像待处理")

            # 处理图像
            image_results, color_analyses = self.process_images(image_paths)

            if not image_results:
                self.logger.warning("没有成功处理任何图像")
                return

            # 生成COCO数据集
            print(f"\n📦 生成COCO数据集...")
            coco_data = self.coco_generator.create_coco_dataset(image_results)

            # 保存COCO数据集
            coco_filename = self.config.get('output.coco.filename', 'dataset.json')
            coco_path = self.coco_dir / coco_filename

            with open(coco_path, 'w', encoding='utf-8') as f:
                json.dump(coco_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"COCO数据集已保存: {coco_path}")

            # 生成处理报告
            if self.config.get('output.reports.generate', True):
                print(f"\n📋 生成处理报告...")
                report_path = self.reports_dir / "processing_report.json"
                self.report_generator.generate_processing_report(
                    image_results, color_analyses, coco_data, str(report_path)
                )

            # 数据集划分（如果启用）
            if self.config.get('dataset_split.enable', False) and len(image_results) > 1:
                print(f"\n✂️ 划分数据集...")
                self._split_dataset(coco_data, coco_path)

            # 打印最终统计
            self._print_final_statistics(image_results, coco_data)

            print(f"\n🎉 处理完成！输出目录: {self.output_dir}")

        except Exception as e:
            self.logger.error(f"处理流程失败: {e}")
            raise

    def _split_dataset(self, coco_data: Dict[str, Any], coco_path: Path):
        """划分数据集"""
        try:
            train_ratio = self.config.get('dataset_split.train_ratio', 0.7)
            val_ratio = self.config.get('dataset_split.val_ratio', 0.2)
            test_ratio = self.config.get('dataset_split.test_ratio', 0.1)
            random_seed = self.config.get('dataset_split.random_seed', 42)

            # 验证比例
            if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
                raise ValueError("数据集划分比例之和必须等于1.0")

            images = coco_data["images"].copy()
            annotations = coco_data["annotations"]

            # 设置随机种子
            np.random.seed(random_seed)
            np.random.shuffle(images)

            # 计算划分点
            total_images = len(images)
            train_end = int(total_images * train_ratio)
            val_end = train_end + int(total_images * val_ratio)

            # 划分图像
            splits = {
                "train": images[:train_end],
                "val": images[train_end:val_end],
                "test": images[val_end:]
            }

            for split_name, split_images in splits.items():
                if not split_images:
                    continue

                # 获取该划分的图像ID
                image_ids = {img["id"] for img in split_images}

                # 筛选对应的标注
                split_annotations = [ann for ann in annotations if ann["image_id"] in image_ids]

                # 创建划分数据集
                split_data = coco_data.copy()
                split_data["images"] = split_images
                split_data["annotations"] = split_annotations

                # 保存划分数据集
                split_filename = f"{coco_path.stem}_{split_name}.json"
                split_path = self.coco_dir / split_filename

                with open(split_path, 'w', encoding='utf-8') as f:
                    json.dump(split_data, f, ensure_ascii=False, indent=2)

                self.logger.info(f"{split_name.upper()}集已保存: {split_path} "
                               f"({len(split_images)} 张图像, {len(split_annotations)} 个标注)")

        except Exception as e:
            self.logger.error(f"数据集划分失败: {e}")
            raise

    def _print_final_statistics(self, image_results: List[Dict[str, Any]], coco_data: Dict[str, Any]):
        """打印最终统计信息"""
        print(f"\n📊 最终统计:")
        print(f"   处理图像数: {len(image_results)}")
        print(f"   总标注数: {len(coco_data['annotations'])}")
        print(f"   类别数: {len(coco_data['categories'])}")

        # 类别分布统计
        category_stats = defaultdict(int)
        for annotation in coco_data["annotations"]:
            category_stats[annotation["category_id"]] += 1

        if category_stats:
            print(f"\n   类别分布:")
            for category in coco_data["categories"]:
                count = category_stats[category["id"]]
                if count > 0:
                    # 获取显示名称
                    display_name = "未知"
                    for cat_info in self.color_analyzer.target_categories.values():
                        if cat_info['id'] == category["id"]:
                            display_name = cat_info['display_name']
                            break
                    print(f"      {display_name}: {count} 个实例")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AirSim语义分割数据COCO格式转换器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用配置文件运行
  python airsim_coco_generator.py --config config.yaml

  # 生成配置文件模板
  python airsim_coco_generator.py --generate-config
        """
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )

    parser.add_argument(
        '--generate-config',
        action='store_true',
        help='生成配置文件模板'
    )

    args = parser.parse_args()

    try:
        if args.generate_config:
            # 生成配置文件模板
            template_path = 'config_template.yaml'
            if os.path.exists(template_path):
                print(f"配置文件模板已存在: {template_path}")
            else:
                print(f"请使用现有的配置文件模板: {template_path}")
            return

        # 检查配置文件
        if not os.path.exists(args.config):
            print(f"❌ 配置文件不存在: {args.config}")
            print(f"💡 使用 --generate-config 生成配置文件模板")
            return

        # 运行处理器
        processor = AirSimCOCOProcessor(args.config)
        processor.run()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()


class AirSimCOCOProcessor:
    """AirSim COCO数据集处理器主类"""

    def __init__(self, config_path: str):
        """
        初始化处理器

        Args:
            config_path: 配置文件路径
        """
        self.config = ConfigManager(config_path)
        self.output_dir = Path(self.config.get('output.base_directory', 'output'))
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        self.coco_dir = self.output_dir / self.config.get('output.subdirectories.coco', 'coco')
        self.viz_dir = self.output_dir / self.config.get('output.subdirectories.visualizations', 'visualizations')
        self.reports_dir = self.output_dir / self.config.get('output.subdirectories.reports', 'reports')

        for dir_path in [self.coco_dir, self.viz_dir, self.reports_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 初始化组件
        self.logger = Logger(self.config, self.output_dir)
        self.color_analyzer = ColorMappingAnalyzer(
            self.config.get('input.csv_file'), self.config, self.logger
        )
        self.coco_generator = COCODatasetGenerator(self.config, self.logger, self.color_analyzer)
        self.viz_generator = VisualizationGenerator(self.config, self.logger, self.color_analyzer)
        self.report_generator = ReportGenerator(self.config, self.logger)

        self.logger.info("AirSim COCO处理器初始化完成")

    def print_detection_statistics(self):
        """打印检测统计信息"""
        print("\n" + "="*60)
        print("🎯 AirSim语义分割数据处理器")
        print("="*60)

        # 项目信息
        print(f"📋 项目名称: {self.config.get('project.name')}")
        print(f"📝 项目描述: {self.config.get('project.description')}")
        print(f"📂 输出目录: {self.output_dir}")

        # CSV文件信息
        csv_file = self.config.get('input.csv_file')
        print(f"\n📊 CSV文件: {csv_file}")
        print(f"   总对象数: {len(self.color_analyzer.color_mapping)}")

        # 目标类别信息
        target_keywords = self.config.get('detection.target_keywords', [])
        print(f"\n🎯 目标关键词: {target_keywords}")
        print(f"   匹配的类别数: {len(self.color_analyzer.target_categories)}")

        if self.color_analyzer.target_categories:
            print("\n🐟 检测到的目标类别:")
            for category_name, category_info in self.color_analyzer.target_categories.items():
                print(f"   {category_info['id']:2d}. {category_info['display_name']:15s} "
                      f"({category_info['instance_count']} 个实例)")

            print("\n🔍 实例详细信息:")
            for color, instance_info in self.color_analyzer.instances.items():
                print(f"   - {instance_info['object_name']:20s} "
                      f"RGB{color} -> {instance_info['display_name']}")

        print("="*60)

    def analyze_images(self, image_paths: List[str]) -> List[Dict[str, Any]]:
        """
        分析图像列表

        Args:
            image_paths: 图像路径列表

        Returns:
            颜色分析结果列表
        """
        analyses = []

        print(f"\n🔍 分析 {len(image_paths)} 张图像...")

        for image_path in tqdm(image_paths, desc="分析图像"):
            try:
                analysis = self.color_analyzer.analyze_image_colors(image_path)
                analyses.append(analysis)

                # 打印分析结果
                print(f"\n📸 图像: {os.path.basename(image_path)}")
                print(f"   尺寸: {analysis['image_size'][1]}x{analysis['image_size'][0]}")
                print(f"   唯一颜色: {analysis['unique_colors_count']} 种")
                print(f"   匹配对象: {len(analysis['matched_objects'])} 个")

                if analysis['target_categories_found']:
                    print("   🎯 检测到的目标类别:")
                    for cat_name, cat_data in analysis['target_categories_found'].items():
                        print(f"      - {cat_data['display_name']}: {len(cat_data['objects'])} 个实例")
                else:
                    print("   ❌ 未检测到目标类别")

                # 显示未匹配的对象（如果配置启用）
                if (self.config.get('detection.show_unmatched_objects', False) and
                    analysis['unmatched_colors']):
                    print("   📋 其他对象:")
                    for obj in analysis['unmatched_colors'][:5]:  # 只显示前5个
                        print(f"      - {obj['object_name']}: {obj['pixel_count']} 像素")
                    if len(analysis['unmatched_colors']) > 5:
                        print(f"      - ... 还有 {len(analysis['unmatched_colors'])-5} 个对象")

            except Exception as e:
                self.logger.error(f"分析图像失败 {image_path}: {e}")
                continue

        return analyses

    def process_images(self, image_paths: List[str]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        处理图像列表

        Args:
            image_paths: 图像路径列表

        Returns:
            (处理结果列表, 颜色分析结果列表)
        """
        # 首先进行颜色分析
        color_analyses = self.analyze_images(image_paths)

        # 然后进行COCO处理
        print(f"\n🔄 处理 {len(image_paths)} 张图像生成COCO数据集...")

        image_results = []

        for i, (image_path, analysis) in enumerate(zip(image_paths, color_analyses), 1):
            try:
                result = self.coco_generator.process_single_image(image_path, i)
                image_results.append(result)

                # 生成可视化（如果启用）
                if self.config.get('output.visualization.generate', True):
                    viz_filename = f"visualization_{i}_{Path(image_path).stem}.png"
                    viz_path = self.viz_dir / viz_filename
                    self.viz_generator.create_visualization(image_path, result, analysis, str(viz_path))

            except Exception as e:
                self.logger.error(f"处理图像失败 {image_path}: {e}")
                continue

        return image_results, color_analyses

    def get_image_paths(self) -> List[str]:
        """获取要处理的图像路径列表"""
        mode = self.config.get('input.mode', 'single')

        if mode == 'single':
            image_path = self.config.get('input.single_image')
            if not image_path or not os.path.exists(image_path):
                raise FileNotFoundError(f"单张图像文件不存在: {image_path}")
            return [image_path]

        elif mode == 'batch':
            batch_dir = self.config.get('input.batch_directory')
            if not batch_dir or not os.path.exists(batch_dir):
                raise FileNotFoundError(f"批量处理目录不存在: {batch_dir}")

            supported_formats = self.config.get('input.supported_formats', ['.jpg', '.png'])
            image_paths = []

            for ext in supported_formats:
                pattern = f"*{ext}"
                image_paths.extend(Path(batch_dir).glob(pattern))
                # 也搜索大写扩展名
                pattern = f"*{ext.upper()}"
                image_paths.extend(Path(batch_dir).glob(pattern))

            if not image_paths:
                raise ValueError(f"在目录 {batch_dir} 中未找到支持的图像文件")

            return [str(path) for path in image_paths]

        else:
            raise ValueError(f"不支持的处理模式: {mode}")

    def run(self):
        """运行完整的处理流程"""
        try:
            # 打印统计信息
            self.print_detection_statistics()

            # 获取图像路径
            image_paths = self.get_image_paths()
            self.logger.info(f"找到 {len(image_paths)} 张图像待处理")

            # 处理图像
            image_results, color_analyses = self.process_images(image_paths)

            if not image_results:
                self.logger.warning("没有成功处理任何图像")
                return

            # 生成COCO数据集
            print(f"\n📦 生成COCO数据集...")
            coco_data = self.coco_generator.create_coco_dataset(image_results)

            # 保存COCO数据集
            coco_filename = self.config.get('output.coco.filename', 'dataset.json')
            coco_path = self.coco_dir / coco_filename

            with open(coco_path, 'w', encoding='utf-8') as f:
                json.dump(coco_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"COCO数据集已保存: {coco_path}")

            # 生成处理报告
            if self.config.get('output.reports.generate', True):
                print(f"\n📋 生成处理报告...")
                report_path = self.reports_dir / "processing_report.json"
                self.report_generator.generate_processing_report(
                    image_results, color_analyses, coco_data, str(report_path)
                )

            # 数据集划分（如果启用）
            if self.config.get('dataset_split.enable', False) and len(image_results) > 1:
                print(f"\n✂️ 划分数据集...")
                self._split_dataset(coco_data, coco_path)

            # 打印最终统计
            self._print_final_statistics(image_results, coco_data)

            print(f"\n🎉 处理完成！输出目录: {self.output_dir}")

        except Exception as e:
            self.logger.error(f"处理流程失败: {e}")
            raise

    def _split_dataset(self, coco_data: Dict[str, Any], coco_path: Path):
        """划分数据集"""
        try:
            train_ratio = self.config.get('dataset_split.train_ratio', 0.7)
            val_ratio = self.config.get('dataset_split.val_ratio', 0.2)
            test_ratio = self.config.get('dataset_split.test_ratio', 0.1)
            random_seed = self.config.get('dataset_split.random_seed', 42)

            # 验证比例
            if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
                raise ValueError("数据集划分比例之和必须等于1.0")

            images = coco_data["images"].copy()
            annotations = coco_data["annotations"]

            # 设置随机种子
            np.random.seed(random_seed)
            np.random.shuffle(images)

            # 计算划分点
            total_images = len(images)
            train_end = int(total_images * train_ratio)
            val_end = train_end + int(total_images * val_ratio)

            # 划分图像
            splits = {
                "train": images[:train_end],
                "val": images[train_end:val_end],
                "test": images[val_end:]
            }

            for split_name, split_images in splits.items():
                if not split_images:
                    continue

                # 获取该划分的图像ID
                image_ids = {img["id"] for img in split_images}

                # 筛选对应的标注
                split_annotations = [ann for ann in annotations if ann["image_id"] in image_ids]

                # 创建划分数据集
                split_data = coco_data.copy()
                split_data["images"] = split_images
                split_data["annotations"] = split_annotations

                # 保存划分数据集
                split_filename = f"{coco_path.stem}_{split_name}.json"
                split_path = self.coco_dir / split_filename

                with open(split_path, 'w', encoding='utf-8') as f:
                    json.dump(split_data, f, ensure_ascii=False, indent=2)

                self.logger.info(f"{split_name.upper()}集已保存: {split_path} "
                               f"({len(split_images)} 张图像, {len(split_annotations)} 个标注)")

        except Exception as e:
            self.logger.error(f"数据集划分失败: {e}")
            raise

    def _print_final_statistics(self, image_results: List[Dict[str, Any]], coco_data: Dict[str, Any]):
        """打印最终统计信息"""
        print(f"\n📊 最终统计:")
        print(f"   处理图像数: {len(image_results)}")
        print(f"   总标注数: {len(coco_data['annotations'])}")
        print(f"   类别数: {len(coco_data['categories'])}")

        # 类别分布统计
        category_stats = defaultdict(int)
        for annotation in coco_data["annotations"]:
            category_stats[annotation["category_id"]] += 1

        if category_stats:
            print(f"\n   类别分布:")
            for category in coco_data["categories"]:
                count = category_stats[category["id"]]
                if count > 0:
                    # 获取显示名称
                    display_name = "未知"
                    for cat_info in self.color_analyzer.target_categories.values():
                        if cat_info['id'] == category["id"]:
                            display_name = cat_info['display_name']
                            break
                    print(f"      {display_name}: {count} 个实例")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AirSim语义分割数据COCO格式转换器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用配置文件运行
  python airsim_coco_generator.py --config config.yaml

  # 生成配置文件模板
  python airsim_coco_generator.py --generate-config
        """
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )

    parser.add_argument(
        '--generate-config',
        action='store_true',
        help='生成配置文件模板'
    )

    args = parser.parse_args()

    try:
        if args.generate_config:
            # 生成配置文件模板
            template_path = 'config_template.yaml'
            if os.path.exists(template_path):
                print(f"配置文件模板已存在: {template_path}")
            else:
                print(f"请使用现有的配置文件模板: {template_path}")
            return

        # 检查配置文件
        if not os.path.exists(args.config):
            print(f"❌ 配置文件不存在: {args.config}")
            print(f"💡 使用 --generate-config 生成配置文件模板")
            return

        # 运行处理器
        processor = AirSimCOCOProcessor(args.config)
        processor.run()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
